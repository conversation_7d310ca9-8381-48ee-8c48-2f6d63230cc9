import { contextBridge, ipc<PERSON><PERSON>er } from "electron";

contextBridge.exposeInMainWorld("electronAPI", {
  onBookDataReceived: (callback: (event: any, data: any) => void) =>
    ipcRenderer.on("book-data-received", callback),

  onQuoteDataReceived: (callback: (event: any, data: any) => void) =>
    ipcRenderer.on("quote-data-received", callback),

  showBrowser: (url: string) => ipcRenderer.send("show-browser", url),
  hideBrowser: () => ipcRenderer.send("hide-browser"),

  fetchWithSession: (params: { url: string; options: any }) =>
    ipcRenderer.invoke("fetch-with-session", params),

  getCookies: (domain: string) => ipcRenderer.invoke("get-cookies", domain),

  getSettings: () => ipcRenderer.invoke("get-settings"),
  saveSettings: (settings: any) =>
    ipcRenderer.invoke("save-settings", settings),
  pickVaultFolder: (): Promise<string> =>
    ipcRenderer.invoke("pick-vault-folder") as Promise<string>,

  exportBooks: (settings: any, booksWithAnnotations: any[]) =>
    ipcRenderer.invoke("export-books", { settings, booksWithAnnotations }),

  getRandomQuote: (settings: any) =>
    ipcRenderer.invoke("get-random-quote", { settings }),

  appendToDailyNote: (settings: any, highlight: any) =>
    ipcRenderer.invoke("append-to-daily-note", { settings, highlight }),

  quitApp: () => ipcRenderer.invoke("quit-app"),

  getLocalIps: () => ipcRenderer.invoke("get-local-ips"),

  storeKindleData: (booksWithAnnotations: any[]) =>
    ipcRenderer.invoke("store-kindle-data", { booksWithAnnotations }),

  getDatabaseStats: () => ipcRenderer.invoke("get-database-stats"),
  getAllSources: () => ipcRenderer.invoke("get-all-sources"),
  deleteSource: (sourceId: number) =>
    ipcRenderer.invoke("delete-source", { sourceId }),
  deleteQuotesBySource: (sourceId: number) =>
    ipcRenderer.invoke("delete-quotes-by-source", { sourceId }),
  clearAllData: () => ipcRenderer.invoke("clear-all-data"),
  toggleSourceIgnored: (sourceId: number, ignored: boolean) =>
    ipcRenderer.invoke("toggle-source-ignored", { sourceId, ignored }),
});
