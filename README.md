# Unearthed Obsidian Plugin (Kindle/KOReader Sync)

Unearthed.app is a free service that seamlessly syncs your Kindle (& KOReader) highlights and notes (all of them) to your preferred platforms.
A **Daily Reflection** is served to you that consists of a quote, your note, book, author and location. This plugin allows you to use custom templates to determine how books will appear in Obsidian. It is smart enough to know what quotes/highlights exist already and just append those that are needed.

Currently, Unearthed supports receiving books from:

-   **Kindle**
-   **KOReader**
-   **Unearthed.app** - file import and/or manual creation

and sending books to:

-   **Obsidian** (via this plugin)
-   **Notion**
-   **Capacities**
-   **Supernotes**
-   And, of course, the **Unearthed** app itself.


## Kindle
Auto Kindle sync is free and without limits. For Kindle books, only the books visible in your Kindle notebook at [Amazon Kindle Notebook](https://read.amazon.com/notebook) will be synced.

## KOReader
Highlights and notes made in the KOReader app can now also be sent to Unearthed, adn therefore Obsidian. These highlights will be merged with those from Kindle along with highlights created manually within Unearthed. If you have the same book in both <PERSON>le and <PERSON>OR<PERSON>er, the highlights will be merged to the same book so that when it appears in Obsidian, all the highlights are within the same book file. KOReader integration is limited to **Premium** users.

## Tags

Unearthed.app now has a comprehensive solution for tagging highlights and notes. You can do this manually (and quickly via inline highlighting text) or you can let AI help you tag and extract ideas. Within unearthed.app you will be able to see a graph view of all of your books, highlights, and tags connected together. These tags will be sent to your Obsidian vault as well, allowing you to connect your ideas together and keep them offline. Tag files will automatically be created in Obsidian and linked to your books. This is limited to **Premium** users.
![image](https://github.com/user-attachments/assets/15ce1a8d-3032-4f32-b4e4-61c9befd1464)
![image](https://github.com/user-attachments/assets/06e1332a-9196-4781-95d7-5b3b1a688efa)

## Video Tutorial (follow along)
Checkout the Youtube channel for some other hopefully useful videos: [Channel](https://www.youtube.com/@CheersCal)
[![Video Tutorial](https://img.youtube.com/vi/uilUlt4wRVs/maxresdefault.jpg)](https://www.youtube.com/watch?v=uilUlt4wRVs)

## Useful Settings

### Choose your templates

Customisable templates allow you to get the data looking just the way you want.
_Beginning and ending the 'Source(book) template' with '---', will result in the data being added as Obsidian Properties._
![image](https://github.com/user-attachments/assets/9b6d1f4c-9a12-410b-ae65-1a3db6a32004)
![image](https://github.com/user-attachments/assets/036e3adf-05cb-4842-b111-3305137cfa68)

### Custom Colours

![image](https://github.com/user-attachments/assets/b36b1082-5784-4e75-9c0d-517b83c08816)

## Daily Reflection

![image](https://github.com/user-attachments/assets/e0bb8af3-1d8c-4037-a38a-89a339b371f4)

## All Quotes and Notes Synced

![image](https://github.com/user-attachments/assets/50bd5fc9-c13e-4c8c-86db-ddba0a88a4cd)

## How to Sync Your Kindle Books with Obsidian

1. Create an account on [unearthed.app](https://unearthed.app).
2. Follow the prompts to sync your Kindle data to Unearthed (this step requires installing a browser extension and creating an Unearthed API key). The browser extension will be linked within unearthed.app, but here are the links again: [Chrome](https://chromewebstore.google.com/detail/unearthed-app/aneeklbnnklhdaipicoakebmbedcgmfb), [Firefox](https://addons.mozilla.org/en-US/firefox/addon/unearthed-app/?utm_source=addons.mozilla.org&utm_medium=referral&utm_content=search)
3. Once you see that Unearthed is receiving books from Kindle, install the Unearthed plugin in your Obsidian vault.
4. Open the plugin settings in Obsidian and paste in another Unearthed API key, then press "Sync."
5. You can set it to auto-sync each time Obsidian loads, or choose to sync manually from the side panel or the plugin settings.

## How to Sync Your KOReader Books with Obsidian

1. Create an account on [unearthed.app](https://unearthed.app).
2. Following the instructions on [Unearthed KOReader Plugin](https://github.com/Unearthed-App/unearthed-koreader) to install the plugin on your device running KOReader. You'll need an Unearthed API key to connect KOReader and Unearthed together.
3. Once you see that Unearthed is receiving books from KOReader, install the Unearthed plugin in your Obsidian vault.
4. Open the plugin settings in Obsidian and paste in another Unearthed API key, then press "Sync."
5. You can set it to auto-sync each time Obsidian loads, or choose to sync manually from the side panel or the plugin settings.

## Open Source & Contributions

Unearthed.app, the browser extension, the Obsidian plugin, and KOReader plugin are all **open source**. I welcome contributions, feedback, and suggestions from the community. If you'd like to get involved, check out our repositories and feel free to submit a pull request or open an issue.
