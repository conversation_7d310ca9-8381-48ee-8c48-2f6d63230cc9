import {
  app,
  BrowserWindow,
  <PERSON><PERSON>er<PERSON>iew,
  ipc<PERSON>ain,
  dialog,
  Tray,
  Menu,
} from "electron";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import { startApiServer, stopApiServer } from "./api/server"; // Assuming this path is correct
import {
  initializeDatabase,
  closeDatabase,
  checkDatabaseHealth,
  getDatabaseSize,
  getDatabasePath,
} from "./db/database";
import { databaseService } from "./services/database-service";
import { dbLogger } from "./utils/logger";
import { DEFAULT_SETTINGS } from "./shared/defaultSettings";

declare const MAIN_WINDOW_WEBPACK_ENTRY: string;
declare const MAIN_WINDOW_PRELOAD_WEBPACK_ENTRY: string;
declare const BROWSER_VIEW_PRELOAD_WEBPACK_ENTRY: string;
// <PERSON>le creating/removing shortcuts on Windows when installing/uninstalling.
if (require("electron-squirrel-startup")) {
  app.quit();
}

let mainWindow: BrowserWindow;
let browserView: BrowserView;
let tray: Electron.Tray | null = null;
let apiServer: any = null;

const createWindow = (): void => {
  // Create the browser window.
  const iconPath =
    process.platform === "win32"
      ? path.join(__dirname, "icon.ico")
      : path.join(__dirname, "icon.png");
  mainWindow = new BrowserWindow({
    height: 800,
    width: 1200,
    icon: iconPath, // Platform-specific icon
    autoHideMenuBar: true,
    webPreferences: {
      preload: MAIN_WINDOW_PRELOAD_WEBPACK_ENTRY,
      nodeIntegration: false,
      contextIsolation: true,
      // webSecurity: false, // Generally keep true unless you have a strong reason not to
    },
  });

  mainWindow.loadURL(MAIN_WINDOW_WEBPACK_ENTRY);

  browserView = new BrowserView({
    webPreferences: {
      preload: BROWSER_VIEW_PRELOAD_WEBPACK_ENTRY,
      nodeIntegration: false,
      contextIsolation: true,
      partition: "persist:main",
    },
  });

  mainWindow.setBrowserView(null);

  mainWindow.on("close", async (e) => {
    const settingsPath = path.join(app.getPath("userData"), "settings.json");
    const settings = fs.existsSync(settingsPath)
      ? JSON.parse(fs.readFileSync(settingsPath, "utf-8"))
      : DEFAULT_SETTINGS;
    if (settings.keepAppRunning) {
      e.preventDefault();
      mainWindow.hide();
      mainWindow.setSkipTaskbar(true);
      setupTray();
    }
  });
};

function handleStartupSetting(runOnStartup: boolean): void {
  const settings = app.getLoginItemSettings();
  const isSet = settings.openAtLogin;

  if (runOnStartup && !isSet) {
    app.setLoginItemSettings({
      openAtLogin: true,
      openAsHidden: true,
    });
    console.log("Enabled run on startup.");
  } else if (!runOnStartup && isSet) {
    app.setLoginItemSettings({
      openAtLogin: false,
    });
    console.log("Disabled run on startup.");
  }
}

function setupIPC() {
  function normalizeColor(color: string) {
    if (!color) return "";
    let c = color.trim().toLowerCase();
    if (c === "grey") c = "gray";

    return c;
  }

  ipcMain.on("show-browser", (event, url: string) => {
    try {
      const { width, height } = mainWindow.getContentBounds();

      const padding = 20;
      const topOffset = 150;
      const bounds = {
        x: padding,
        y: topOffset,
        width: width - padding * 2,
        height: height - topOffset - padding,
      };

      browserView.setBounds(bounds);

      mainWindow.setBrowserView(browserView);

      browserView.webContents.loadURL(url).catch((err) => {
        console.error("Failed to load URL in BrowserView:", err);
      });
    } catch (error) {
      console.error("Error showing browser:", error);
    }
  });

  ipcMain.on("hide-browser", () => {
    try {
      mainWindow.setBrowserView(null);
    } catch (error) {
      console.error("Error hiding browser:", error);
    }
  });

  ipcMain.handle("get-cookies", async (event, domain) => {
    const cookies = await browserView.webContents.session.cookies.get({
      domain,
    });
    return cookies;
  });

  ipcMain.handle("fetch-with-session", async (event, { url, options }) => {
    try {
      const session = browserView.webContents.session;

      const fetchOptions = {
        method: options.method || "GET",
        headers: options.headers || {},
      };

      const response = await session.fetch(url, fetchOptions);
      const responseText = await response.text();

      return {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        data: responseText,
      };
    } catch (error) {
      console.error("Error in fetch-with-session:", error);
      throw error;
    }
  });

  const settingsPath = path.join(app.getPath("userData"), "settings.json");
  const defaultSettings = DEFAULT_SETTINGS;

  ipcMain.handle("get-settings", async () => {
    try {
      if (!fs.existsSync(settingsPath)) {
        fs.writeFileSync(
          settingsPath,
          JSON.stringify(defaultSettings, null, 2)
        );
        return defaultSettings;
      }
      const data = fs.readFileSync(settingsPath, "utf-8");
      return JSON.parse(data);
    } catch (err) {
      return defaultSettings;
    }
  });

  ipcMain.handle("save-settings", async (event, settings) => {
    try {
      // Read current settings to compare tokenSecret
      let currentTokenSecret = "";
      if (fs.existsSync(settingsPath)) {
        const currentSettings = JSON.parse(
          fs.readFileSync(settingsPath, "utf-8")
        );
        currentTokenSecret = currentSettings.tokenSecret || "";
      }

      fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2));

      handleStartupSetting(settings.runOnStartup);

      // Check if tokenSecret changed and restart API server if needed
      if (settings.tokenSecret !== currentTokenSecret) {
        dbLogger.info("tokenSecret changed, restarting API server");
        try {
          if (apiServer) {
            stopApiServer(apiServer);
            apiServer = null;
          }
          initializeApiServer();
        } catch (error) {
          dbLogger.error("Failed to restart API server:", error);
          return {
            success: false,
            error: `Settings saved but failed to restart API server: ${error.message}`,
          };
        }
      }

      return { success: true };
    } catch (err) {
      return { success: false, error: err.message };
    }
  });

  ipcMain.handle("pick-vault-folder", async () => {
    const result = await dialog.showOpenDialog({
      properties: ["openDirectory"],
      title: "Select Obsidian Vault Folder",
    });
    if (result.canceled || !result.filePaths.length) return "";
    return result.filePaths[0];
  });

  ipcMain.handle(
    "export-books",
    async (event, { settings, booksWithAnnotations }) => {
      if (!settings) return { success: false, error: "No settings provided." };

      const vaultPath = settings.obsidianVaultPath;
      if (!vaultPath) return { success: false, error: "No vault path set." };

      // Get data from database instead of using passed-in data
      let sourcesWithQuotes;
      try {
        sourcesWithQuotes = await databaseService.getAllSourcesWithQuotes();
        // Filter out ignored sources
        sourcesWithQuotes = sourcesWithQuotes.filter(
          (source) => !source.ignored
        );
        if (!sourcesWithQuotes || sourcesWithQuotes.length === 0) {
          return { success: false, error: "No books found in database." };
        }
      } catch (error) {
        console.error("Error retrieving data from database:", error);
        return {
          success: false,
          error: "Failed to retrieve data from database.",
        };
      }

      // Transform database data to match expected format
      const booksWithAnnotationsFromDb = sourcesWithQuotes.map((source) => ({
        title: source.title,
        subtitle: source.subtitle || "",
        author: source.author || "",
        type: source.type || "Book",
        origin: source.origin || "",
        asin: source.asin || "",
        annotations: source.quotes.map((quote) => ({
          quote: quote.content,
          note: quote.note || "",
          location: quote.location || "",
          color: quote.color || "",
        })),
      }));

      const rootFolder = settings.rootFolder || "Unearthed";
      const parentFolderPath = path.join(vaultPath, rootFolder);
      const typeFolders: Record<string, string> = {};
      const errors: any[] = [];
      let filesWritten = 0;
      const HIDDEN_CHAR = "\u200B";

      function extractExistingQuotes(fileContent: string): string[] {
        const quoteRegex = new RegExp(
          `${HIDDEN_CHAR}(.+?)${HIDDEN_CHAR}`,
          "gs"
        );
        const quotes = [];
        let match;
        while ((match = quoteRegex.exec(fileContent)) !== null) {
          quotes.push(match[1].trim());
        }
        return quotes;
      }

      function hexToRgba(hex: string, alpha = 1) {
        hex = hex.replace(/^#/, "");
        if (hex.length === 3) {
          hex = hex
            .split("")
            .map((x) => x + x)
            .join("");
        }
        const num = parseInt(hex, 16);
        const r = (num >> 16) & 255;
        const g = (num >> 8) & 255;
        const b = num & 255;
        return `rgba(${r},${g},${b},${alpha})`;
      }

      function createQuoteBlock(ann: any) {
        let quoteBlock =
          settings.quoteTemplate ||
          "#### {{content}}\n\n**Note:** {{note}}\n**Location:** {{location}}\n**Color:** {{color}}";

        const hiddenContent = `${HIDDEN_CHAR}${ann.quote}${HIDDEN_CHAR}`;
        let styledContent = hiddenContent;

        if (settings.quoteColorMode !== "none" && ann.color) {
          const colorLower = normalizeColor(ann.color);
          let colorHex = "";
          const colorKeys = [
            "yellow",
            "blue",
            "pink",
            "orange",
            "red",
            "green",
            "olive",
            "cyan",
            "purple",
            "gray",
          ];
          for (const colorKey of colorKeys) {
            if (colorLower.includes(colorKey)) {
              colorHex =
                (settings.customColors && settings.customColors[colorKey]) ||
                (settings.DEFAULT_COLOR_MAP
                  ? settings.DEFAULT_COLOR_MAP[colorKey]
                  : undefined) ||
                "";
              break;
            }
          }
          if (colorHex) {
            if (settings.quoteColorMode === "background") {
              const colorRgba0 = hexToRgba(colorHex, 0.5);
              styledContent = `<div style="background: linear-gradient(to right, ${colorHex} 0%, ${colorRgba0} 100%); color: #fff; padding: 24px 28px; border-radius: 18px; box-shadow: 0 4px 18px rgba(33,140,228,0.12), 0 1.5px 4px rgba(0,0,0,0.08); font-size: 1.15rem; letter-spacing: 0.01em; line-height: 1.6;">${hiddenContent}</div>`;
            } else if (settings.quoteColorMode === "text") {
              styledContent = `<span style="color: ${colorHex};">${hiddenContent}</span>`;
            }
          }
        }

        quoteBlock = quoteBlock.replace(/{{content}}/g, styledContent);
        quoteBlock = quoteBlock.replace(/{{note}}/g, ann.note || "");
        quoteBlock = quoteBlock.replace(/{{location}}/g, ann.location || "");
        quoteBlock = quoteBlock.replace(/{{color}}/g, ann.color || "");
        return quoteBlock;
      }

      function ensureDirSync(dir: string) {
        if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
      }

      function toSafeFileName(str: string) {
        if (typeof str !== "string") return "untitled";
        let result = str;
        if (settings.sourceFilenameLowercase) result = result.toLowerCase();
        result = result.replace(
          /[\\/:*?"<>|]/g,
          settings.sourceFilenameReplaceSpaces || "-"
        );
        result = result.replace(
          /\s+/g,
          settings.sourceFilenameReplaceSpaces || "-"
        );
        result = result.replace(
          /-+/g,
          settings.sourceFilenameReplaceSpaces || "-"
        );
        result = result.replace(/^[-]+|[-]+$/g, "");
        return result;
      }

      // Helper: apply template
      function applyTemplate(template: string, data: any) {
        let out = template;
        Object.keys(data).forEach((key) => {
          out = out.replace(new RegExp(`{{${key}}}`, "g"), data[key] || "");
        });
        return out;
      }

      try {
        ensureDirSync(parentFolderPath);
        const types = Array.from(
          new Set(
            (booksWithAnnotationsFromDb as any[]).map(
              (b: any) => b.type || "Book"
            )
          )
        );
        for (const type of types) {
          const folder = path.join(parentFolderPath, `${type}s`);
          ensureDirSync(folder);
          typeFolders[type] = folder;
        }

        for (const book of booksWithAnnotationsFromDb as any[]) {
          const folder = typeFolders[book.type || "Book"];
          let fileName = settings.sourceFilenameTemplate || "{{title}}";
          fileName = applyTemplate(fileName, book);
          fileName = toSafeFileName(fileName);
          const filePath = path.join(folder, fileName + ".md");

          try {
            if (fs.existsSync(filePath)) {
              let fileContent = fs.readFileSync(filePath, "utf-8");
              const existingQuotes = extractExistingQuotes(fileContent);
              let quotesAdded = 0;

              if (
                Array.isArray(book.annotations) &&
                book.annotations.length > 0
              ) {
                for (const ann of book.annotations) {
                  if (!existingQuotes.includes(ann.quote)) {
                    quotesAdded++;
                    const quoteBlock = createQuoteBlock(ann);
                    fileContent += `\n---\n\n${quoteBlock}\n`;
                  }
                }
              }
              if (quotesAdded > 0) {
                fs.writeFileSync(filePath, fileContent, "utf-8");
              }
              filesWritten++;
            } else {
              let fileContent = `---
title: ${book.title}
subtitle: ${book.subtitle}
author: "[[${book.author}]]"
type: ${book.type}
origin: ${book.origin}
asin: ${book.asin}
tags: ${book.type},${book.origin}
---\n\n`;
              if (settings.sourceTemplate) {
                fileContent = applyTemplate(settings.sourceTemplate, book);
              }

              if (
                Array.isArray(book.annotations) &&
                book.annotations.length > 0
              ) {
                for (const ann of book.annotations) {
                  const quoteBlock = createQuoteBlock(ann);
                  fileContent += `\n\n${quoteBlock}\n`;
                }
              }

              fs.writeFileSync(filePath, fileContent, "utf-8");
              filesWritten++;
            }
          } catch (err: any) {
            errors.push({ file: filePath, error: err.message });
          }
        }
        return { success: true, filesWritten, errors };
      } catch (err: any) {
        return { success: false, error: err.message, errors };
      }
    }
  );

  // Define the HIDDEN_CHAR, must match the one used in export-books
  const HIDDEN_CHAR = "\u200B"; // Zero-width space

  // Function to recursively get all markdown files
  async function getAllMarkdownFiles(dirPath: string): Promise<string[]> {
    let files: string[] = [];
    try {
      const dirents = await fs.promises.readdir(dirPath, {
        withFileTypes: true,
      });
      for (const dirent of dirents) {
        const fullPath = path.join(dirPath, dirent.name);
        if (dirent.isDirectory()) {
          files = files.concat(await getAllMarkdownFiles(fullPath));
        } else if (dirent.isFile() && dirent.name.endsWith(".md")) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${dirPath}:`, error);
    }
    return files;
  }

  interface ExtractedQuote {
    fileName: string;
    bookTitle: string;
    author: string;
    quote: string;
    location?: string;
    color?: string;
    note?: string;
  }

  async function extractQuotesFromFile(
    filePath: string
  ): Promise<ExtractedQuote[]> {
    const quotes: ExtractedQuote[] = [];
    try {
      const fileContent = await fs.promises.readFile(filePath, "utf-8");
      const noteFilename = path.basename(filePath, ".md");

      let author = "Unknown";
      let bookTitle = noteFilename;

      const frontmatterMatch = fileContent.match(/^---\s*([\s\S]*?)\s*---/);
      if (frontmatterMatch) {
        const frontmatter = frontmatterMatch[1];

        const authorMatch = frontmatter.match(/^(?:author|Author):\s*(.+)$/m);
        if (authorMatch) {
          author = authorMatch[1].trim();
        }

        const titleMatch = frontmatter.match(/^(?:title|Title):\s*(.+)$/m);
        if (titleMatch) {
          bookTitle = titleMatch[1].trim();
        }
      }

      const quoteRegex = new RegExp(`${HIDDEN_CHAR}(.+?)${HIDDEN_CHAR}`, "gs");
      let match;

      while ((match = quoteRegex.exec(fileContent)) !== null) {
        const quoteContent = match[1].trim();

        quotes.push({
          fileName: noteFilename,
          bookTitle: bookTitle,
          author: author,
          quote: quoteContent,
        });
      }
    } catch (error) {
      console.error(`Error reading or parsing file ${filePath}:`, error);
    }
    return quotes;
  }

  ipcMain.handle("get-random-quote", async (event, { settings }) => {
    try {
      // Get a random quote from the database
      const randomQuoteWithSource = await databaseService.getRandomQuote();

      if (!randomQuoteWithSource) {
        return {
          success: false,
          error:
            "No quotes found in the database. Please import some books first.",
        };
      }

      // Get total quote count for statistics
      const stats = await databaseService.getStats();

      // Transform database quote to match the expected ExtractedQuote format
      const transformedQuote: ExtractedQuote = {
        fileName: `${randomQuoteWithSource.source.title}.md`, // Generate filename from book title
        bookTitle: randomQuoteWithSource.source.title,
        author: randomQuoteWithSource.source.author || "Unknown Author",
        quote: randomQuoteWithSource.content,
        location: randomQuoteWithSource.location || undefined,
        color: randomQuoteWithSource.color || undefined,
        note: randomQuoteWithSource.note || undefined,
      };

      return {
        success: true,
        quote: transformedQuote,
        totalQuotes: stats.quoteCount,
      };
    } catch (error: any) {
      console.error("Error in get-random-quote:", error);
      return {
        success: false,
        error: `Failed to retrieve random quote from database: ${error.message}`,
      };
    }
  });

  ipcMain.handle(
    "append-to-daily-note",
    async (event, { settings, highlight }) => {
      try {
        let templateToUse = `---\n> {{quote}}\n\n**From:** {{bookTitle}} by [[{{author}}]]
\n**File:** [[{{fileName}}]]
\n\n---\n\n`;

        if (settings.dailyReflectionTemplate) {
          templateToUse = settings.dailyReflectionTemplate;
        }

        let parsedHighlight = highlight;
        if (typeof highlight === "string") {
          try {
            parsedHighlight = JSON.parse(highlight);
          } catch (parseError) {
            console.error("Failed to parse highlight JSON:", parseError);
            return { success: false, error: "Invalid highlight data format" };
          }
        }

        let content = templateToUse;
        content = content.replace("{{quote}}", parsedHighlight.quote || "");
        content = content.replace(
          "{{bookTitle}}",
          parsedHighlight.bookTitle || ""
        );
        content = content.replace("{{author}}", parsedHighlight.author || "");
        content = content.replace(
          "{{fileName}}",
          parsedHighlight.fileName || ""
        );

        const vaultPath = settings.obsidianVaultPath;
        if (!vaultPath) return { success: false, error: "No vault path set." };
        const location = settings.dailyReflectionLocation || "Daily Notes";
        const dateFormat = settings.dailyReflectionDateFormat || "YYYY-MM-DD";
        const createIfNotExist = !!settings.createDailyNoteIfNotExist;
        // Format today's date
        const today = new Date();
        const pad = (n: number) => n.toString().padStart(2, "0");
        const yyyy = today.getFullYear();
        const mm = pad(today.getMonth() + 1);
        const dd = pad(today.getDate());
        let dateStr = `${yyyy}-${mm}-${dd}`;
        if (dateFormat === "YYYY-MM-DD") {
          dateStr = `${yyyy}-${mm}-${dd}`;
        } else if (dateFormat === "YYYYMMDD") {
          dateStr = `${yyyy}${mm}${dd}`;
        } else if (dateFormat === "DD-MM-YYYY") {
          dateStr = `${dd}-${mm}-${yyyy}`;
        } // Add more formats as needed
        // Build the file path
        const dailyNoteDir = path.join(vaultPath, location);
        if (!fs.existsSync(dailyNoteDir)) {
          if (createIfNotExist) {
            fs.mkdirSync(dailyNoteDir, { recursive: true });
          } else {
            return {
              success: false,
              error: `Daily note folder does not exist: ${dailyNoteDir}`,
            };
          }
        }
        const filePath = path.join(dailyNoteDir, `${dateStr}.md`);
        if (!fs.existsSync(filePath)) {
          if (createIfNotExist) {
            fs.writeFileSync(filePath, "\n\n" + content, "utf-8");
            return { success: true, filePath };
          } else {
            return {
              success: false,
              error: `Daily note file does not exist: ${filePath}`,
            };
          }
        }
        // Append the content (with a separator)
        let fileContent = fs.readFileSync(filePath, "utf-8");
        fileContent += `\n\n${content}`;
        fs.writeFileSync(filePath, fileContent, "utf-8");
        return { success: true, filePath };
      } catch (err: any) {
        return { success: false, error: err.message };
      }
    }
  );

  ipcMain.handle("quit-app", () => {
    if (mainWindow) {
      mainWindow.removeAllListeners("close");
    }
    app.quit();
  });

  ipcMain.handle("get-local-ips", () => {
    const toReturn = [];
    try {
      const interfaces = os.networkInterfaces();

      for (const name of Object.keys(interfaces)) {
        const iface = interfaces[name];
        if (iface) {
          for (const ifaceInfo of iface) {
            const { address, family, internal } = ifaceInfo;
            if (family === "IPv4" && !internal) {
              toReturn.push(address);
            }
          }
        }
      }
    } catch (error) {
      console.error("Error getting local IP:", error);
    }
    return toReturn;
  });

  // Database management IPC handlers
  ipcMain.handle("database-health-check", async () => {
    try {
      const isHealthy = await checkDatabaseHealth();
      const size = getDatabaseSize();
      const path = getDatabasePath();

      return {
        success: true,
        healthy: isHealthy,
        size,
        path,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  });

  ipcMain.handle("reinitialize-database", async () => {
    try {
      await closeDatabase();
      await initializeDatabaseServices();
      return { success: true };
    } catch (error) {
      dbLogger.error("Failed to reinitialize database:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  });

  ipcMain.handle("database-service-status", async () => {
    try {
      const stats = await databaseService.getStats();
      const isHealthy = await checkDatabaseHealth();

      return {
        success: true,
        healthy: isHealthy,
        stats,
        serviceReady: true,
      };
    } catch (error) {
      dbLogger.error("Failed to get database service status:", error);
      return {
        success: false,
        error: error.message,
        serviceReady: false,
      };
    }
  });

  ipcMain.handle("get-database-stats", async () => {
    try {
      const stats = await databaseService.getStats();
      return {
        success: true,
        stats,
      };
    } catch (error) {
      dbLogger.error("Failed to get database stats:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  });

  ipcMain.handle("get-all-sources", async () => {
    try {
      const sources = await databaseService.getAllSourcesWithCounts();
      return {
        success: true,
        sources,
      };
    } catch (error) {
      dbLogger.error("Failed to get all sources:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  });

  ipcMain.handle("delete-source", async (event, { sourceId }) => {
    try {
      const deleted = await databaseService.deleteSource(sourceId);
      return {
        success: deleted,
        message: deleted ? "Source deleted successfully" : "Source not found",
      };
    } catch (error) {
      dbLogger.error("Failed to delete source:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  });

  ipcMain.handle("delete-quotes-by-source", async (event, { sourceId }) => {
    try {
      const quotes = await databaseService.getQuotesBySourceId(sourceId);
      let deletedCount = 0;

      for (const quote of quotes) {
        const deleted = await databaseService.deleteQuote(quote.id);
        if (deleted) deletedCount++;
      }

      return {
        success: true,
        deletedCount,
        message: `Deleted ${deletedCount} quotes from source`,
      };
    } catch (error) {
      dbLogger.error("Failed to delete quotes by source:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  });

  ipcMain.handle("clear-all-data", async () => {
    try {
      await databaseService.clearAllData();
      return {
        success: true,
        message: "All data cleared successfully",
      };
    } catch (error) {
      dbLogger.error("Failed to clear all data:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  });

  ipcMain.handle("test-database-connection", async () => {
    try {
      // Test database service initialization
      await databaseService.initialize();
      return { success: true, message: "Database connection test passed" };
    } catch (error) {
      dbLogger.error("Database connection test failed:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  });

  // Store Kindle data in database
  ipcMain.handle(
    "store-kindle-data",
    async (event, { booksWithAnnotations }) => {
      try {
        console.log(`Storing ${booksWithAnnotations.length} books in database`);

        for (const book of booksWithAnnotations) {
          // Transform book data to match database schema
          const sourceData = {
            title: book.title || "Untitled",
            subtitle: book.subtitle || null,
            author: book.author || "",
            type: book.type || "Book",
            origin: book.origin || "Kindle",
            asin: book.asin || null,
          };

          // Upsert the source (book)
          const savedSource = await databaseService.upsertSource(sourceData);

          // Transform and store annotations
          if (book.annotations && Array.isArray(book.annotations)) {
            for (const annotation of book.annotations) {
              const quoteData = {
                content: annotation.quote || "",
                note: annotation.note || "",
                color: normalizeColor(annotation.color || ""),
                location: annotation.location || null,
                sourceId: savedSource.id,
              };

              await databaseService.upsertQuote(quoteData);
            }
          }
        }

        console.log("Successfully stored Kindle data in database");
        return { success: true };
      } catch (error) {
        console.error("Failed to store Kindle data:", error);
        return {
          success: false,
          error: error.message,
        };
      }
    }
  );

  ipcMain.handle(
    "toggle-source-ignored",
    async (event, { sourceId, ignored }) => {
      try {
        const updated = await databaseService.toggleSourceIgnored(
          sourceId,
          ignored
        );
        return {
          success: !!updated,
          source: updated,
        };
      } catch (error) {
        dbLogger.error("Failed to toggle ignored state for source:", error);
        return {
          success: false,
          error: error.message,
        };
      }
    }
  );
}

/**
 * Initialize the API server with database-ready callbacks
 */
function initializeApiServer(): void {
  if (apiServer) {
    dbLogger.warn("API server already initialized");
    return;
  }

  dbLogger.info("Initializing API server");

  const settingsPath = path.join(app.getPath("userData"), "settings.json");
  let tokenSecret = "";
  
  try {
    if (fs.existsSync(settingsPath)) {
      const settings = JSON.parse(fs.readFileSync(settingsPath, "utf-8"));
      tokenSecret = settings.tokenSecret || "";
    }
  } catch (error) {
    dbLogger.error("Failed to load settings for API server:", error);
  }

  apiServer = startApiServer(
    (bookData) => {
      if (mainWindow && mainWindow.webContents) {
        mainWindow.webContents.send("book-data-received", bookData);
      } else {
        dbLogger.warn("mainWindow not ready; book data not sent:", bookData);
      }
    },
    (quoteData) => {
      if (mainWindow && mainWindow.webContents) {
        // loop quoteData and make new key called quote which = content
        const quoteDataWithQuoteKey = quoteData.map((quote: any) => {
          return {
            quote: quote.content,
            note: quote.note,
            sourceName: quote.sourceName,
            sourceId: quote.sourceId,
            location: quote.location,
            color: quote.color,
          };
        });
        mainWindow.webContents.send(
          "quote-data-received",
          quoteDataWithQuoteKey
        );
      } else {
        dbLogger.warn("mainWindow not ready; quote data not sent:", quoteData);
      }
    },
    tokenSecret
  );

  dbLogger.info("API server initialized successfully");
}

/**
 * Initialize database and related services
 */
async function initializeDatabaseServices(): Promise<void> {
  try {
    dbLogger.info("Starting database services initialization");

    // Step 1: Initialize the database connection
    dbLogger.debug("Initializing database connection");
    await initializeDatabase();
    dbLogger.info("Database connection initialized");

    // Step 2: Initialize the database service
    dbLogger.debug("Initializing database service");
    await databaseService.initialize();
    dbLogger.info("Database service initialized");

    // Step 3: Verify database health
    dbLogger.debug("Performing database health check");
    const isHealthy = await checkDatabaseHealth();
    if (!isHealthy) {
      throw new Error("Database health check failed");
    }
    dbLogger.info("Database health check passed");

    // Step 4: Initialize API server (now that database is ready)
    dbLogger.debug("Initializing API server");
    initializeApiServer();

    dbLogger.info("All database services initialized successfully");
  } catch (error) {
    dbLogger.error("Failed to initialize database services", error);
    throw error;
  }
}

/**
 * Close all services gracefully
 */
async function closeAllServices(): Promise<void> {
  try {
    dbLogger.info("Closing all services");

    // Close API server if it exists
    if (apiServer) {
      try {
        apiServer.close();
        dbLogger.info("API server closed");
      } catch (error) {
        dbLogger.error("Error closing API server:", error);
      }
      apiServer = null;
    }

    // Close database connection
    try {
      await closeDatabase();
      dbLogger.info("Database connection closed");
    } catch (error) {
      dbLogger.error("Error closing database:", error);
    }
  } catch (error) {
    dbLogger.error("Error during service shutdown:", error);
  }
}

function setupTray() {
  if (tray) return;
  tray = new Tray(path.join(__dirname, "icon.png"));
  const contextMenu = Menu.buildFromTemplate([
    {
      label: "Show App",
      click: () => {
        if (mainWindow) {
          mainWindow.show();
          mainWindow.setSkipTaskbar(false);
        }
      },
    },
    {
      label: "Quit",
      click: () => {
        if (mainWindow) {
          mainWindow.removeAllListeners("close");
        }
        app.quit();
      },
    },
  ]);
  tray.setToolTip("Unearthed");
  tray.setContextMenu(contextMenu);
  tray.on("double-click", () => {
    if (mainWindow) {
      mainWindow.show();
      mainWindow.setSkipTaskbar(false);
    }
  });
}
app.whenReady().then(async () => {
  try {
    dbLogger.info("Starting application initialization");
    await initializeDatabaseServices();
    dbLogger.info("Database services initialization completed");

    const settingsPath = path.join(app.getPath("userData"), "settings.json");
    const settings = fs.existsSync(settingsPath)
      ? JSON.parse(fs.readFileSync(settingsPath, "utf-8"))
      : DEFAULT_SETTINGS;

    handleStartupSetting(settings.runOnStartup);

    // Create window and setup IPC after database is ready
    createWindow();
    setupIPC();

    dbLogger.info("Application initialization completed successfully");
  } catch (error) {
    dbLogger.error(
      "Failed to initialize database services during app startup:",
      error
    );

    // Show error dialog to user
    dialog.showErrorBox(
      "Database Initialization Error",
      `Failed to initialize the database services: ${error.message}\n\nThe app may not function correctly.`
    );

    // Continue with app startup even if database fails
    dbLogger.warn(
      "Continuing with app startup despite database initialization failure"
    );

    const settingsPath = path.join(app.getPath("userData"), "settings.json");
    const settings = fs.existsSync(settingsPath)
      ? JSON.parse(fs.readFileSync(settingsPath, "utf-8"))
      : DEFAULT_SETTINGS;
    handleStartupSetting(settings.runOnStartup);

    createWindow();
    setupIPC();

    // Try to initialize API server without database dependency
    try {
      initializeApiServer();
    } catch (apiError) {
      dbLogger.error("Failed to initialize API server:", apiError);
    }
  }

  app.on("activate", () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on("window-all-closed", async () => {
  // Close database connection before quitting
  try {
    await closeAllServices();
  } catch (error) {
    dbLogger.error("Error closing services:", error);
  }

  if (process.platform !== "darwin") {
    app.quit();
  }
});

app.on("before-quit", async () => {
  // Ensure database is closed when app is quitting
  try {
    await closeAllServices();
  } catch (error) {
    dbLogger.error("Error closing services on quit:", error);
  }
});
