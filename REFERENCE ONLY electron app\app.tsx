import "./index.css";
import * as React from "react";
import { useState, useEffect, useCallback, useRef } from "react"; // Import hooks
import {
  BookOpen,
  Wifi,
  Download,
  CheckCircle,
  RefreshCw,
  AlertCircle,
  Clock,
  WifiOff,
  Lightbulb,
  Palette,
  MapPin,
  User,
  Shuffle,
  Copy,
  Link,
  BookUserIcon,
  XCircle,
  Cog,
  Sun,
  Moon,
  Eye,
  EyeOff,
} from "lucide-react";
import { SettingsModal } from "./components/SettingsModal";
import { createRoot } from "react-dom/client";

declare global {
  interface Window {
    electronAPI: {
      showBrowser: (url: string) => void;
      hideBrowser: () => void;
      fetchWithSession: (params: { url: string; options: any }) => Promise<any>;
      getCookies: (domain: string) => Promise<any[]>;
      getSettings: () => Promise<any>;
      saveSettings: (settings: any) => Promise<any>;
      pickVaultFolder: () => Promise<string>;
      exportBooks: (settings: any, books: any[]) => Promise<any>;
      getRandomQuote: (settings: any) => Promise<any>;
      appendToDailyNote: (settings: any, markdown: string) => Promise<any>;
      getLocalIps: () => Promise<string>;
      quitApp: () => void;
      storeKindleData: (booksWithAnnotations: any[]) => Promise<any>;
      getDatabaseStats: () => Promise<any>;
      getAllSources: () => Promise<{
        success: boolean;
        sources: Array<any & { quoteCount: number; noteCount: number }>;
        error?: string;
      }>;
      deleteSource: (sourceId: number) => Promise<any>;
      deleteQuotesBySource: (sourceId: number) => Promise<any>;
      clearAllData: () => Promise<any>;

      // Add this line:
      onBookDataReceived: (callback: (event: any, data: any) => void) => void;
      onQuoteDataReceived: (callback: (event: any, data: any) => void) => void;
      toggleSourceIgnored: (sourceId: number, ignored: boolean) => Promise<any>;
    };
  }
}

const PORT = 6543;
const AUTO_SYNC_INTERVAL = 3600000; // 1 hour

const App: React.FC = () => {
  const [browserVisible, setBrowserVisible] = React.useState<boolean>(false);
  const [isDark, setIsDark] = React.useState<boolean>(true);

  const [responseData, setResponseData] = React.useState<string>(
    "No data fetched yet."
  );
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [booksWithAnnotations, setBooksWithAnnotations] = React.useState<any[]>(
    []
  );
  const [settings, setSettings] = useState<any | null>(null);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [apiUrls, setApiUrls] = useState<string[]>(["http://localhost:6543"]);
  const [dailyReflection, setDailyReflection] = useState<any>(null);
  const [dailyReflectionError, setDailyReflectionError] = useState<
    string | null
  >(null);
  const [copySuccess, setCopySuccess] = useState<string | null>(null);
  const [dailyAppendedAlready, setDailyAppendedAlready] = useState(false);
  const [sentToObsidianAlready, setSentToObsidianAlready] = useState(false);
  const [cachedHighlights, setCachedHighlights] = useState<any[]>([]);
  const [appendStatus, setAppendStatus] = useState<string>(""); // 'cache' or 'fetched'
  const [dataSource, setDataSource] = useState<string>("");
  const [incomingBookData, setIncomingBookData] = useState<any[]>([]);
  const [lastBookDataReceivedTime, setLastBookDataReceivedTime] = useState<
    string | null
  >(null);
  const [lastKindleSyncTime, setLastKindleSyncTime] = useState<string | null>(
    null
  );
  const [connectionStatus, setConnectionStatus] = useState<{
    connected: boolean;
    loading: boolean;
    error?: string;
    lastChecked?: string;
  }>({ connected: false, loading: false });

  const [databaseStats, setDatabaseStats] = useState<{
    sourceCount: number;
    quoteCount: number;
  } | null>(null);
  const [allSources, setAllSources] = useState<any[]>([]);
  const [showSources, setShowSources] = useState(false);
  const [statsLoading, setStatsLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState<{
    clearAll: boolean;
    sources: { [key: number]: boolean };
  }>({ clearAll: false, sources: {} });

  const loadDatabaseStats = useCallback(async () => {
    setStatsLoading(true);
    try {
      const statsResult = await window.electronAPI.getDatabaseStats();
      if (statsResult.success) {
        setDatabaseStats(statsResult.stats);
        console.log("Database stats loaded:", statsResult.stats);
      }

      const sourcesResult = await window.electronAPI.getAllSources();
      if (sourcesResult.success) {
        setAllSources(sourcesResult.sources);
        console.log("All sources loaded:", sourcesResult.sources);
      }
    } catch (error) {
      console.error("Error loading database stats:", error);
    } finally {
      setStatsLoading(false);
    }
  }, []);

  const handleClearAllData = async () => {
    if (
      !confirm(
        "Are you sure you want to delete ALL sources and quotes? This action cannot be undone."
      )
    ) {
      return;
    }

    setDeleteLoading((prev) => ({ ...prev, clearAll: true }));
    try {
      const result = await window.electronAPI.clearAllData();
      if (result.success) {
        setResponseData("All data cleared successfully");
        await loadDatabaseStats();
      } else {
        setResponseData(`Failed to clear data: ${result.error}`);
      }
    } catch (error) {
      console.error("Error clearing all data:", error);
      setResponseData(
        `Error clearing data: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setDeleteLoading((prev) => ({ ...prev, clearAll: false }));
    }
  };

  const handleDeleteSource = async (sourceId: number, sourceTitle: string) => {
    if (
      !confirm(
        `Are you sure you want to delete "${sourceTitle}" and all its quotes? This action cannot be undone.`
      )
    ) {
      return;
    }

    setDeleteLoading((prev) => ({
      ...prev,
      sources: { ...prev.sources, [sourceId]: true },
    }));

    try {
      const result = await window.electronAPI.deleteSource(sourceId);
      if (result.success) {
        setResponseData(`Source "${sourceTitle}" deleted successfully`);
        await loadDatabaseStats();
      } else {
        setResponseData(`Failed to delete source: ${result.error}`);
      }
    } catch (error) {
      console.error("Error deleting source:", error);
      setResponseData(
        `Error deleting source: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setDeleteLoading((prev) => ({
        ...prev,
        sources: { ...prev.sources, [sourceId]: false },
      }));
    }
  };

  const handleDeleteQuotesBySource = async (
    sourceId: number,
    sourceTitle: string
  ) => {
    if (
      !confirm(
        `Are you sure you want to delete all quotes from "${sourceTitle}"? The source will be kept. This action cannot be undone.`
      )
    ) {
      return;
    }

    setDeleteLoading((prev) => ({
      ...prev,
      sources: { ...prev.sources, [sourceId]: true },
    }));

    try {
      const result = await window.electronAPI.deleteQuotesBySource(sourceId);
      if (result.success) {
        setResponseData(
          `Deleted ${result.deletedCount} quotes from "${sourceTitle}"`
        );
        await loadDatabaseStats();
      } else {
        setResponseData(`Failed to delete quotes: ${result.error}`);
      }
    } catch (error) {
      console.error("Error deleting quotes:", error);
      setResponseData(
        `Error deleting quotes: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setDeleteLoading((prev) => ({
        ...prev,
        sources: { ...prev.sources, [sourceId]: false },
      }));
    }
  };

  const checkKindleConnection = useCallback(async () => {
    setConnectionStatus((prev) => ({
      ...prev,
      loading: true,
      error: undefined,
    }));
    try {
      const response = await window.electronAPI.fetchWithSession({
        url: `${settingsRef?.current?.kindleUrl}/kindle-library/search?libraryType=BOOKS&sortType=recency&querySize=1`,
        options: {
          method: "GET",
          headers: {
            Accept: "application/json",
            "Cache-Control": "no-cache",
          },
        },
      });

      if (response && response.status === 200) {
        try {
          const data =
            typeof response.data === "string"
              ? JSON.parse(response.data)
              : response.data;
          if (data && typeof data === "object") {
            setConnectionStatus({
              connected: true,
              loading: false,
              lastChecked: new Date().toLocaleTimeString(),
            });
            return true;
          }
          throw new Error("Invalid response format");
        } catch (e) {
          console.error("Error parsing response:", e);
          throw new Error("Invalid JSON response");
        }
      } else {
        console.error("Error fetching Kindle library:");
        throw new Error(`HTTP ${response?.status || "No response"}`);
      }
    } catch (error) {
      console.error("Error checking Kindle connection:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      setConnectionStatus({
        connected: false,
        loading: false,
        error: `Connection error: ${errorMessage}`,
      });
      return false;
    }
  }, []);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape" && browserVisible) {
        window.electronAPI.hideBrowser();
        setBrowserVisible(false);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [browserVisible]);

  useEffect(() => {
    if (settings) {
      const updatedSettings = { ...settings, isDark };
      setSettings(updatedSettings);
      window.electronAPI.saveSettings(updatedSettings);
    }
  }, [isDark]);

  useEffect(() => {
    const timer = setTimeout(() => {
      checkKindleConnection();
    }, 2000);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    loadDatabaseStats();
  }, [loadDatabaseStats]);

  useEffect(() => {
    const loadSettings = async () => {
      const settings = await window.electronAPI.getSettings();
      setSettings(settings);

      if (settings && typeof settings.isDark === "boolean") {
        setIsDark(settings.isDark);
      }
    };
    loadSettings();

    const getLocalIps = async () => {
      try {
        const ip = await window.electronAPI.getLocalIps();

        const toSet: string[] = [];
        if (ip && Array.isArray(ip) && ip.length > 0) {
          ip.forEach((i: any) => {
            toSet.push(`http://${i}:${PORT}`);
          });

          setApiUrls(toSet);
        } else {
          setApiUrls([`http://localhost:${PORT}`]);
        }
      } catch (error) {
        console.error("Could not get local IP, using localhost", error);
        setApiUrls([`http://localhost:${PORT}`]);
      }
    };

    getLocalIps();
  }, []);

  const handleSettingsChange = (key: string, value: any) => {
    setSettings((prev: any) => ({ ...prev, [key]: value }));
  };

  const handleColorChange = (color: string, value: string) => {
    setSettings((prev: any) => ({
      ...prev,
      customColors: { ...prev.customColors, [color]: value },
    }));
  };

  // Save settings
  const saveSettings = async () => {
    try {
      const result = await window.electronAPI.saveSettings(settings);
      if (!result.success) {
        throw new Error(result.error || "Failed to save");
      }
    } catch (e: any) {
      throw new Error(e.message || "Failed to save settings");
    }
  };

  const pickVaultFolder = async (): Promise<string> => {
    const folder = await window.electronAPI.pickVaultFolder();
    if (folder) {
      handleSettingsChange("obsidianVaultPath", folder);
      return folder;
    }
    return "";
  };

  const showBrowser = () => {
    if (!settingsRef.current?.kindleUrl) return;

    let formattedUrl = settingsRef?.current?.kindleUrl;
    if (
      !formattedUrl.startsWith("http://") &&
      !formattedUrl.startsWith("https://")
    ) {
      formattedUrl = "https://" + formattedUrl;
    }

    window.electronAPI.showBrowser(formattedUrl);
    setBrowserVisible(true);
  };

  const formatAuthorName = useCallback((author: string): string => {
    if (!author) return "Unknown";
    if (author.endsWith(":")) {
      author = author.slice(0, -1);
    }
    const parts = author.split(",").map((part) => part.trim());
    if (parts.length === 2) {
      const [lastName, firstName] = parts;
      return `${firstName} ${lastName}`;
    } else {
      return author;
    }
  }, []);

  const parseBooks = (itemsList: any[]): any[] => {
    if (!Array.isArray(itemsList)) return [];
    return itemsList.map((book) => {
      const rawBookTitle = book.title?.trim() || "Untitled";
      const titleParts = rawBookTitle.split(": ");
      const bookTitle = titleParts[0] || "Untitled";
      const bookSubtitle = titleParts[1] || "";
      const bookAuthor =
        book.authors && book.authors[0]
          ? formatAuthorName(book.authors[0])
          : "Unknown";
      return {
        asin: book.asin,
        title: bookTitle,
        subtitle: bookSubtitle,
        author: bookAuthor,
        imageUrl: book.productUrl,
      };
    });
  };

  const sanitizeText = (text: string): string => {
    if (!text || typeof text !== "string") return "";
    let out = text
      .normalize("NFKC")
      .replace(/\u2018/g, "'")
      .replace(/\u2019/g, "'")
      .replace(/\u201C/g, '"')
      .replace(/\u201D/g, '"')
      .replace(/\u2013/g, "-")
      .replace(/\u2014/g, "-")
      .replace(/\u2026/g, "...")
      .replace(/\u00A0/g, " ")
      .replace(/\u1680/g, " ")
      .replace(/\u202F|\u205F|\u3000/g, " ")
      .replace(/\u200B/g, "")
      .replace(/\u200C/g, "")
      .replace(/\u200D/g, "")
      .replace(/\uFEFF/g, "")
      .replace(/\u00AD/g, "");
    [
      "\u2000",
      "\u2001",
      "\u2002",
      "\u2003",
      "\u2004",
      "\u2005",
      "\u2006",
      "\u2007",
      "\u2008",
      "\u2009",
      "\u200A",
    ].forEach((cp) => {
      out = out.split(cp).join(" ");
    });
    // Remove control characters \u0000-\u0008, \u000B, \u000C, \u000E-\u001F, \u007F
    [
      "\u0000",
      "\u0001",
      "\u0002",
      "\u0003",
      "\u0004",
      "\u0005",
      "\u0006",
      "\u0007",
      "\u0008",
      "\u000B",
      "\u000C",
      "\u000E",
      "\u000F",
      "\u0010",
      "\u0011",
      "\u0012",
      "\u0013",
      "\u0014",
      "\u0015",
      "\u0016",
      "\u0017",
      "\u0018",
      "\u0019",
      "\u001A",
      "\u001B",
      "\u001C",
      "\u001D",
      "\u001E",
      "\u001F",
      "\u007F",
    ].forEach((cp) => {
      out = out.split(cp).join("");
    });
    return out.replace(/\s+/g, " ").trim();
  };

  const parseNotebookHtml = (html: string) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, "text/html");

    const continuationTokenInput = doc.querySelector(
      ".kp-notebook-annotations-next-page-start"
    );
    const continuationToken = continuationTokenInput
      ? (continuationTokenInput as HTMLInputElement).value
      : null;

    const contentLimitStateInput = doc.querySelector(
      ".kp-notebook-content-limit-state"
    );
    const contentLimitState = contentLimitStateInput
      ? (contentLimitStateInput as HTMLInputElement).value
      : null;

    const annotations: any[] = [];
    const quotes = doc.querySelectorAll("#highlight");
    const notes = doc.querySelectorAll("#note");
    const colorsAndLocations = doc.querySelectorAll(
      "#annotationHighlightHeader"
    );
    const length = Math.min(
      quotes.length,
      notes.length,
      colorsAndLocations.length
    );
    for (let i = 0; i < length; i++) {
      const rawQuote = quotes[i].textContent?.trim() || "";
      const rawNote = notes[i].textContent?.trim() || "";
      const rawColorAndLocation =
        colorsAndLocations[i].textContent?.trim() || "";
      const [rawColor = "", rawLocation = ""] =
        rawColorAndLocation.split(" | ");
      annotations.push({
        quote: sanitizeText(rawQuote),
        note: sanitizeText(rawNote),
        color: sanitizeText(rawColor),
        location: sanitizeText(rawLocation),
      });
    }
    return { annotations, continuationToken, contentLimitState };
  };

  const fetchBookAnnotations = async (book: any) => {
    const allAnnotations: any[] = [];
    let continuationToken: string | null = null;
    let contentLimitState: string | null = null;
    let page = 0;
    do {
      let url = `${settingsRef?.current?.kindleUrl}/notebook?asin=${book.asin}`;
      if (continuationToken) {
        url += `&token=${encodeURIComponent(
          continuationToken
        )}&contentLimitState=${encodeURIComponent(contentLimitState || "")}`;
      } else {
        url += `&contentLimitState=&`;
      }
      // Use fetchWithSession to preserve cookies/session
      const result = await window.electronAPI.fetchWithSession({
        url,
        options: {
          credentials: "include",
          headers: {
            Accept: "*/*",
            "x-requested-with": "XMLHttpRequest",
          },
          referrerPolicy: "strict-origin-when-cross-origin",
          method: "GET",
          mode: "cors",
        },
      });
      if (result.status === 200) {
        const {
          annotations,
          continuationToken: nextToken,
          contentLimitState: nextContentLimitState,
        } = parseNotebookHtml(result.data);
        allAnnotations.push(...annotations);
        continuationToken = nextToken;
        contentLimitState = nextContentLimitState;
      } else {
        break;
      }
      page++;

      if (page > 20) break;
    } while (continuationToken);
    return { ...book, annotations: allAnnotations };
  };

  const updateHighlightCache = (enrichedBooks: any[]) => {
    const allHighlights: any[] = [];
    enrichedBooks.forEach((book: any) => {
      if (Array.isArray(book.annotations)) {
        book.annotations.forEach((ann: any) => {
          allHighlights.push({
            ...ann,
            bookTitle: book.title,
            author: book.author,
          });
        });
      }
    });
    setCachedHighlights(allHighlights);
  };

  const fetchHighlightsForToday = useCallback(async (): Promise<unknown[]> => {
    setIsLoading(true);
    setResponseData("Fetching Kindle data...");

    try {
      const urlToFetch = `${settingsRef?.current?.kindleUrl}/kindle-library/search?libraryType=BOOKS&sortType=recency&querySize=50`;
      const allItems: unknown[] = [];
      let paginationToken: string | null = null;
      do {
        let fetchUrl = urlToFetch;
        if (paginationToken) {
          fetchUrl += `&paginationToken=${encodeURIComponent(paginationToken)}`;
        }
        const result = await window.electronAPI.fetchWithSession({
          url: fetchUrl,
          options: {
            credentials: "include",
            headers: {
              Accept: "*/*",
              "validation-token": "undefined",
              "Sec-Fetch-Dest": "empty",
              "Sec-Fetch-Site": "same-origin",
              Priority: "u=4",
            },
            referrer: `${settingsRef?.current?.kindleUrl}/kindle-library`,
            method: "GET",
            mode: "cors",
          },
        });
        if (result.status === 200) {
          const res = JSON.parse(result.data);
          const itemsList = res.itemsList || [];
          allItems.push(...itemsList);
          paginationToken = res.paginationToken;
        } else {
          throw new Error(`Error: ${result.status} - ${result.statusText}`);
        }
      } while (paginationToken);

      const parsed = parseBooks(allItems);
      setResponseData(
        `Received ${parsed.length} kindle books. Fetching highlights...`
      );
      // Fetch highlights for each book
      const enrichedBooks: unknown[] = [];
      for (let i = 0; i < parsed.length; i++) {
        parsed[i].origin = "Kindle";
        parsed[i].type = "Book";
        try {
          const enriched = await fetchBookAnnotations(parsed[i]);
          enrichedBooks.push(enriched);
        } catch (e) {
          enrichedBooks.push({
            ...parsed[i],
            annotations: [],
            error: (e as Error).message,
          });
        }
      }

      updateHighlightCache(enrichedBooks);
      setBooksWithAnnotations(enrichedBooks);

      // Store fetched data in database
      try {
        setResponseData("Storing data in database...");
        await window.electronAPI.storeKindleData(enrichedBooks);
        console.log("Successfully stored Kindle data in database");

        // Refresh database stats after storing new data
        await loadDatabaseStats();
      } catch (error) {
        console.error("Failed to store Kindle data in database:", error);
        // Continue with the process even if database storage fails
      }

      let summaryText = `Exporting...\nTotal books: ${enrichedBooks.length}\n`;
      summaryText += enrichedBooks
        .map(
          (book: any) =>
            `- ${book.title}: ${
              book.annotations ? book.annotations.length : 0
            } highlights`
        )
        .join("\n");

      setResponseData(summaryText);
      updateHighlightCache(enrichedBooks);
      setDataSource("fetched");

      return enrichedBooks;
    } catch (error: unknown) {
      setResponseData(`Error`);
    } finally {
      setIsLoading(false);
    }
  }, [cachedHighlights]);

  const incomingBookDataRef = useRef(incomingBookData);
  useEffect(() => {
    incomingBookDataRef.current = incomingBookData;
  }, [incomingBookData]);

  useEffect(() => {
    const handler = async (_event: unknown, data: unknown) => {
      const bookData = Array.isArray(data) ? data : [];

      const processedBookData = bookData.map((book: any) => ({
        ...book,
        type: "Book",
        origin: "KOReader",
      }));

      setIncomingBookData(processedBookData);
      setLastBookDataReceivedTime(new Date().toLocaleTimeString());

      // Note: Book data is already stored by the API server, no need to store again here
      // The API server handles the database storage for KOReader data
    };

    window.electronAPI?.onBookDataReceived(handler);
  }, []);

  useEffect(() => {
    const handler = async (_event: unknown, data: unknown[]) => {
      const currentData = incomingBookDataRef.current || [];
      const updatedIncomingBookData = Array.isArray(currentData)
        ? [...currentData]
        : [];

      data.forEach((quote: any) => {
        const book = updatedIncomingBookData.find(
          (book: any) => book.title === quote.sourceName
        );
        if (book) {
          book.annotations = [...(book.annotations || []), quote];
        }
      });

      updatedIncomingBookData.forEach((book) => {
        book.type = "Book";
        book.origin = "KOReader";
      });

      setIncomingBookData(updatedIncomingBookData);
      setLastBookDataReceivedTime(new Date().toLocaleTimeString());

      // Note: Quote data is already stored by the API server, no need to store again here
      // The API server handles the database storage for KOReader data

      await handleExportIncoming(updatedIncomingBookData);

      // Refresh database stats after receiving new data
      console.log("Refreshing database stats after KOReader data storage...");
      await loadDatabaseStats();
      console.log("Database stats refreshed");
    };

    window.electronAPI?.onQuoteDataReceived(handler);
  }, []);

  const appendDailyReflection = useCallback(
    async (highlight: unknown) => {
      if (!highlight) return;

      const result = await window.electronAPI.appendToDailyNote(
        settings,
        typeof highlight === "string" ? highlight : JSON.stringify(highlight)
      );
      if (result.success) {
        setDailyAppendedAlready(true);
        setAppendStatus("Appended to your daily note in Obsidian.");
      } else {
        setAppendStatus("");
        setDailyReflectionError(
          result.error || "Failed to append to daily note."
        );
      }
    },
    [settings]
  );

  const handleExport = async () => {
    setResponseData("Exporting...");
    try {
      const result = await window.electronAPI.exportBooks(
        settings,
        booksWithAnnotationsRef.current
      );
      if (result.success) {
        setLastKindleSyncTime(new Date().toLocaleTimeString());
        setSentToObsidianAlready(true);
        setResponseData(`Exported ${result.filesWritten} files successfully.`);
        if (result.errors && result.errors.length > 0) {
          setResponseData(
            `Some files failed: ${result.errors
              .map((e: unknown) => {
                const error = e as { file?: string; error?: string };
                return `${error.file || "Unknown file"}: ${
                  error.error || "Unknown error"
                }`;
              })
              .join("; ")}`
          );
        }
      } else {
        setResponseData(result.error || "Export failed.");
      }
    } catch (e: unknown) {
      setResponseData(e instanceof Error ? e.message : String(e));
    }
  };

  const settingsRef = useRef(settings);

  useEffect(() => {
    settingsRef.current = settings;
  }, [settings]);

  const handleExportIncoming = async (data: unknown[] = []) => {
    if (!data || data.length === 0) return;

    try {
      let dataToExport: unknown[] = [];
      if (data && data.length > 0) {
        dataToExport = data;
      } else {
        const currentData = incomingBookDataRef.current;
        dataToExport = Array.isArray(currentData) ? currentData : [];
      }

      const result = await window.electronAPI.exportBooks(
        settingsRef.current,
        dataToExport
      );
      if (result.success) {
        // setExportStatus(`Exported ${result.filesWritten} files successfully.`);
        if (result.errors && result.errors.length > 0) {
          // setExportError(
          //   `Some files failed: ${result.errors
          //     .map((e: any) => e.file + ": " + e.error)
          //     .join("; ")}`
          // );
        }
      } else {
        // setExportStatus("");
        // setExportError(result.error || "Export failed.");
      }
    } catch (e: unknown) {
      console.log(e instanceof Error ? e.message : String(e));
    }
  };

  const dailyReflectionRef = useRef(dailyReflection);
  useEffect(() => {
    dailyReflectionRef.current = dailyReflection;
  }, [dailyReflection]);

  const dailyAppendedAlreadyRef = useRef(dailyAppendedAlready);
  useEffect(() => {
    dailyAppendedAlreadyRef.current = dailyAppendedAlready;
  }, [dailyAppendedAlready]);

  const getRandomHighlight = async (append = true) => {
    const result = await window.electronAPI.getRandomQuote(settings);
    if (result.success) {
      setDailyReflection(result.quote);
      setDailyReflectionError("");

      if (append) {
        await appendDailyReflection(result.quote);
      }

      return result.quote;
    } else {
      setDailyReflectionError(result.error);
    }
    return null;
  };

  const runDailyReflection = async () => {
    if (!settingsRef.current || !settingsRef.current.addDailyReflection) return;

    let newQuote = null;
    if (!dailyReflectionRef.current) {
      newQuote = await getRandomHighlight(false);
    } else {
      newQuote = dailyReflectionRef.current;
    }

    if (newQuote && !dailyAppendedAlreadyRef.current) {
      await appendDailyReflection(newQuote);
    }
  };

  useEffect(() => {
    let dailyReflectionInterval: NodeJS.Timeout | null = null;

    runDailyReflection();

    if (dailyAppendedAlreadyRef.current) {
      clearInterval(dailyReflectionInterval);
    } else {
      if (settingsRef.current && settingsRef.current.addDailyReflection) {
        dailyReflectionInterval = setInterval(runDailyReflection, 10000);
      }
    }

    return () => {
      if (dailyReflectionInterval) clearInterval(dailyReflectionInterval);
    };
  }, [settingsRef.current, dailyAppendedAlreadyRef.current]);

  const cachedHighlightsRef = useRef(cachedHighlights);
  useEffect(() => {
    cachedHighlightsRef.current = cachedHighlights;
  }, [cachedHighlights]);

  const sentToObsidianAlreadyRef = useRef(sentToObsidianAlready);
  useEffect(() => {
    sentToObsidianAlreadyRef.current = sentToObsidianAlready;
  }, [sentToObsidianAlready]);

  const booksWithAnnotationsRef = useRef(booksWithAnnotations);
  useEffect(() => {
    booksWithAnnotationsRef.current = booksWithAnnotations;
  }, [booksWithAnnotations]);

  // Separate state for tracking sync status
  const [syncStatus, setSyncStatus] = useState<{
    hasRunInitial: boolean;
    isRunning: boolean;
    lastError: string | null;
    nextSyncTime: string | null;
  }>({
    hasRunInitial: false,
    isRunning: false,
    lastError: null,
    nextSyncTime: null,
  });

  useEffect(() => {
    if (!settings || !settings.obsidianVaultPath || syncStatus.hasRunInitial) {
      return;
    }

    const runInitialSync = async () => {
      setSyncStatus((prev) => ({ ...prev, isRunning: true, lastError: null }));

      try {
        if (cachedHighlightsRef.current.length === 0) {
          await fetchHighlightsForToday();
        }

        if (
          cachedHighlightsRef.current.length > 0 &&
          !sentToObsidianAlreadyRef.current
        ) {
          if (booksWithAnnotationsRef.current.length > 0) {
            await handleExport();
            await handleExportIncoming();
          } else {
            setResponseData(
              "Initial sync completed. No books with annotations found to export."
            );
          }
        } else {
          setResponseData("Initial sync completed. No new data to export.");
        }

        setSyncStatus((prev) => ({
          ...prev,
          hasRunInitial: true,
          isRunning: false,
          nextSyncTime: settings.autoSync
            ? new Date(Date.now() + AUTO_SYNC_INTERVAL).toLocaleTimeString()
            : null,
        }));
      } catch (error) {
        console.error("Initial sync failed:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error occurred";
        setSyncStatus((prev) => ({
          ...prev,
          hasRunInitial: true,
          isRunning: false,
          lastError: errorMessage,
        }));
        setResponseData(`Initial sync failed: ${errorMessage}`);
      }
    };

    runInitialSync();
  }, [settings, syncStatus.hasRunInitial, cachedHighlights]);

  useEffect(() => {
    if (
      !settings?.autoSync ||
      !syncStatus.hasRunInitial ||
      syncStatus.lastError
    ) {
      return;
    }

    let ongoingSyncInterval: NodeJS.Timeout | null = null;

    const runOngoingSync = async () => {
      console.log("Running ongoing sync");
      setSyncStatus((prev) => ({ ...prev, isRunning: true, lastError: null }));

      try {
        if (cachedHighlightsRef.current.length === 0) {
          console.log("No cached highlights found, fetching from Kindle");
          await fetchHighlightsForToday();
        }

        if (
          cachedHighlightsRef.current.length > 0 &&
          booksWithAnnotationsRef.current.length > 0
        ) {
          console.log(
            `Auto-sync: Exporting ${booksWithAnnotationsRef.current.length} books with annotations`
          );
          await handleFetchAll();

          setResponseData(
            `Auto-sync completed. Exported ${
              booksWithAnnotationsRef.current.length
            } books at ${new Date().toLocaleTimeString()}`
          );
        } else {
          console.log("Auto-sync: No new data to export");
          setResponseData(
            `Auto-sync completed. No new data to export at ${new Date().toLocaleTimeString()}`
          );
        }

        setSyncStatus((prev) => ({
          ...prev,
          isRunning: false,
          nextSyncTime: new Date(
            Date.now() + AUTO_SYNC_INTERVAL
          ).toLocaleTimeString(),
        }));
      } catch (error) {
        console.error("Ongoing sync failed:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error occurred";
        setSyncStatus((prev) => ({
          ...prev,
          isRunning: false,
          lastError: errorMessage,
        }));
        setResponseData(`Auto-sync failed: ${errorMessage}`);
      }
    };

    // Set up the interval for ongoing sync
    ongoingSyncInterval = setInterval(runOngoingSync, AUTO_SYNC_INTERVAL);

    return () => {
      if (ongoingSyncInterval) {
        clearInterval(ongoingSyncInterval);
      }
    };
  }, [
    settings,
    syncStatus.hasRunInitial,
    syncStatus.lastError,
    cachedHighlights,
  ]);

  const handleFetchAll = async () => {
    await fetchHighlightsForToday();
    if (settings?.obsidianVaultPath && booksWithAnnotations.length > 0) {
      await handleExport();
      await handleExportIncoming();
      setLastKindleSyncTime(new Date().toLocaleTimeString());
    }
  };

  const getStatusColor = () => {
    if (connectionStatus.loading) return "from-yellow-400 to-orange-400";
    if (connectionStatus.connected) return "from-green-400 to-emerald-400";
    return "from-red-400 to-rose-400";
  };

  const getStatusIcon = () => {
    if (connectionStatus.loading)
      return <RefreshCw className="w-3 h-3 animate-spin" />;
    if (connectionStatus.connected) return <Wifi className="w-3 h-3" />;
    return <WifiOff className="w-3 h-3" />;
  };

  // Toggle ignored state for a source
  const handleToggleIgnored = async (
    sourceId: number,
    currentIgnored: boolean
  ) => {
    try {
      const result = await window.electronAPI.toggleSourceIgnored(
        sourceId,
        !currentIgnored
      );
      if (result.success) {
        setResponseData(
          `Source ${result.source.title} is now ${
            result.source.ignored ? "ignored" : "active"
          }`
        );
        await loadDatabaseStats();
      } else {
        setResponseData(`Failed to update ignored state: ${result.error}`);
      }
    } catch (error) {
      setResponseData(
        `Error updating ignored state: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  };

  // Before rendering, calculate counts:
  const ignoredCount = allSources.filter((s) => s.ignored).length;
  const activeCount = allSources.length - ignoredCount;

  return (
    <>
      {browserVisible && (
        <>
          <button
            onClick={() => {
              window.electronAPI.hideBrowser();
              setBrowserVisible(false);
            }}
            className="cursor-pointer fixed top-4 left-1/2 transform -translate-x-1/2 group flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 text-white text-xl font-bold rounded-xl shadow-lg hover:from-red-700 hover:to-red-800 transition-all duration-300 active:scale-95 z-50 focus:outline-none focus:ring-2 focus:ring-red-400/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-neutral-900 overflow-hidden"
            title="Close Browser"
          >
            <span className="absolute inset-0 bg-gradient-to-r from-white/0 to-white/10 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></span>
            <XCircle className="w-5 h-5 mr-2 relative z-10" />
            <span className="relative z-10">Close Browser</span>
          </button>
          <div className="fixed w-full h-full bg-black opacity-50 z-40"></div>
        </>
      )}
      <div
        className={`${
          isDark && "dark"
        } flex flex-col h-screen w-screen bg-gray-50 text-gray-900 dark:bg-gray-950 dark:text-white overflow-hidden`}
      >
        <div className="flex items-center gap-4 px-4 py-2 bg-gray-100 dark:bg-neutral-800 border-b border-gray-200 dark:border-neutral-700 text-sm">
          <div className="flex items-center gap-1.5">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              Kindle:
            </span>
            <div className="flex items-center gap-1">
              {connectionStatus.loading ? (
                <RefreshCw className="w-3 h-3 animate-spin text-yellow-500" />
              ) : connectionStatus.connected ? (
                <CheckCircle className="w-3 h-3 text-green-500" />
              ) : (
                <XCircle className="w-3 h-3 text-red-500" />
              )}
              <span
                className={`text-xs ${
                  connectionStatus.connected
                    ? "text-green-600 dark:text-green-400"
                    : "text-red-600 dark:text-red-400"
                }`}
              >
                {connectionStatus.connected ? "Connected" : "Disconnected"}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-1.5">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              Obsidian:
            </span>
            <div className="flex items-center gap-1">
              {settings?.obsidianVaultPath ? (
                <>
                  <CheckCircle className="w-3 h-3 text-green-500" />
                  <span className="text-xs text-green-600 dark:text-green-400">
                    Vault Set
                  </span>
                </>
              ) : (
                <>
                  <XCircle className="w-3 h-3 text-red-500" />
                  <span className="text-xs text-red-600 dark:text-red-400">
                    No Vault
                  </span>
                </>
              )}
            </div>
          </div>
        </div>
        <div className="flex flex-col flex-grow h-full overflow-y-auto p-8">
          <header className="relative mb-6">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-indigo-600/10 rounded-2xl blur-xl"></div>
            <div className="relative flex flex-col sm:flex-row justify-between items-center gap-6 p-8 bg-gradient-to-br from-white via-gray-50/80 to-white rounded-3xl border border-gray-200/60 backdrop-blur-sm shadow-[0_8px_32px_rgba(0,0,0,0.08)] group overflow-hidden dark:from-neutral-900/90 dark:via-neutral-800/80 dark:to-neutral-900/90 dark:border-neutral-700/40 dark:shadow-[0_8px_32px_rgba(0,0,0,0.3)]">
              <div className="absolute inset-0 z-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1200 ease-out"></div>

              <div className="flex flex-col sm:flex-row items-center gap-8 relative z-10">
                <div className="text-center sm:text-left">
                  <h1 className="text-6xl font-black mb-3 tracking-tight leading-none">
                    <span className="bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500 bg-clip-text text-transparent drop-shadow-sm">
                      Unearthed{" "}
                    </span>
                    <span className="relative inline-block text-gray-800 dark:text-neutral-200 pb-2">
                      <span className="relative z-10">Local</span>
                      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-violet-500 to-purple-500 rounded-full transform scale-x-100 origin-left"></div>
                    </span>
                  </h1>
                  <p className="text-gray-600 dark:text-neutral-400 text-base font-medium tracking-wide">
                    Keep it close to home
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-4 relative z-10">
                <button
                  onClick={() => setSettingsOpen(true)}
                  className="cursor-pointer group relative flex items-center gap-3 px-6 py-3 h-18 bg-gradient-to-r from-gray-100/80 to-gray-200/80 hover:from-gray-200/90 hover:to-gray-300/90 text-gray-800 rounded-2xl shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-gray-400/50 focus:ring-offset-2 focus:ring-offset-white transition-all duration-300 transform hover:scale-[1.02] overflow-hidden backdrop-blur-sm dark:from-neutral-700/80 dark:to-neutral-600/80 dark:hover:from-neutral-600/90 dark:hover:to-neutral-500/90 dark:text-white dark:focus:ring-neutral-400/50 dark:focus:ring-offset-neutral-900"
                  title="Open Settings"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/15 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-800"></div>
                  <div className="relative flex items-center gap-3">
                    <div className="p-2 bg-white/20 dark:bg-black/20 rounded-xl group-hover:bg-white/30 dark:group-hover:bg-black/30 transition-all duration-200">
                      <Cog className="w-5 h-5 group-hover:rotate-90 transition-transform duration-300" />
                    </div>
                    <span className="font-semibold text-sm">Settings</span>
                  </div>
                </button>

                <button
                  onClick={() => setIsDark(!isDark)}
                  className="cursor-pointer group relative h-18 w-18 bg-gradient-to-br from-amber-400/90 to-orange-400/90 hover:from-amber-300 hover:to-orange-300 dark:from-indigo-500/90 dark:to-purple-500/90 dark:hover:from-indigo-400 dark:hover:to-purple-400 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform focus:outline-none focus:ring-2 focus:ring-amber-300 dark:focus:ring-indigo-300 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-neutral-900 overflow-hidden"
                  title={
                    isDark ? "Switch to Light Mode" : "Switch to Dark Mode"
                  }
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative w-full h-full flex justify-center items-center">
                    {isDark ? (
                      <Sun className="w-6 h-6 text-white group-hover:rotate-180 transition-transform duration-500" />
                    ) : (
                      <Moon className="w-6 h-6 text-white group-hover:-rotate-12 transition-transform duration-300" />
                    )}
                  </div>
                </button>
              </div>
            </div>
            <div className="mt-6 px-4 max-w-2xl mx-auto">
              <div className="p-4 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 border border-indigo-500/20 rounded-xl backdrop-blur-sm">
                <div className="flex items-start gap-3">
                  <div className="p-1.5 bg-indigo-500/20 rounded-lg mt-0.5 flex-shrink-0">
                    <svg
                      className="w-4 h-4 text-indigo-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                      This app currently supports syncing from{" "}
                      <span className="font-medium text-gray-900 dark:text-white">
                        Kindle
                      </span>{" "}
                      and{" "}
                      <span className="font-medium text-gray-900 dark:text-white">
                        KOReader
                      </span>{" "}
                      (via the Unearthed KOReader Plugin). Data from all sources
                      will be merged together in your file system. The app is
                      currently focused on syncing to Obsidian, you do not need
                      a special Obsidian plugin if you have this app.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </header>

          <div className="flex flex-col w-full max-w-4xl mx-auto">
            {incomingBookData && lastBookDataReceivedTime && (
              <div className="shadow-pink-600/10 bg-pink-100 text-pink-500 border-pink-700 dark:shadow-pink-600/10 dark:bg-pink-800/20 dark:text-pink-200 dark:border-pink-700 rounded-2xl mb-6 border p-4 shadow-xl">
                <div className="text-xl font-semibold mb-2 flex space-x-2 items-center">
                  <BookOpen />
                  <span>
                    KOReader: Book Data Received at {lastBookDataReceivedTime}
                  </span>
                </div>
                <p>
                  Total books: {incomingBookData.length}
                  <br />
                  They will be merged with your other files
                </p>
              </div>
            )}

            <div className="relative bg-gradient-to-br from-white to-gray-50 dark:from-neutral-900 dark:via-neutral-800 dark:to-neutral-900 rounded-2xl p-8 shadow-2xl border border-gray-200 dark:border-neutral-700/50 backdrop-blur-sm overflow-hidden mb-8">
              <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/5 via-purple-500/5 to-blue-500/5 rounded-2xl pointer-events-none"></div>

              <div className="absolute top-4 right-4 w-16 h-16 bg-gradient-to-br from-yellow-400/20 to-orange-400/20 rounded-full blur-xl animate-pulse"></div>
              <div className="absolute bottom-4 left-4 w-12 h-12 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000"></div>

              <div className="relative z-10">
                <div className="flex flex-col sm:flex-row justify-between items-center mb-8 gap-6">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl shadow-lg">
                      <Lightbulb className="w-7 h-7 text-white" />
                    </div>
                    <div>
                      <h3 className="text-3xl font-bold text-gray-800 dark:text-white bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text">
                        Daily Reflection
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
                        Discover wisdom from your reading experiences
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-3 justify-end">
                    {dailyReflection && (
                      <button
                        onClick={async (
                          e: React.MouseEvent<HTMLButtonElement>
                        ) => {
                          e.preventDefault();
                          const textToCopy = `"${dailyReflection.quote}"\n\n— ${dailyReflection.bookTitle} by ${dailyReflection.author}`;
                          try {
                            await navigator.clipboard.writeText(textToCopy);
                            setCopySuccess("Copied to clipboard!");
                            setTimeout(() => setCopySuccess(null), 2000);
                          } catch (err) {
                            console.error("Failed to copy:", err);
                            setCopySuccess("Failed to copy");
                            setTimeout(() => setCopySuccess(null), 2000);
                          }
                        }}
                        className="cursor-pointer group relative flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 active:scale-95 overflow-hidden"
                        title="Copy reflection to clipboard"
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-white/0 to-white/10 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                        <Copy className="w-4 h-4 relative z-10" />
                        <span className="relative z-10">Copy Reflection</span>
                      </button>
                    )}

                    <button
                      onClick={() => getRandomHighlight()}
                      className="cursor-pointer group relative flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-semibold rounded-xl shadow-lg hover:from-purple-700 hover:to-purple-800 transition-all duration-300 active:scale-95 disabled:opacity-60 disabled:cursor-not-allowed overflow-hidden"
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-white/0 to-white/10 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                      <div className="relative z-10 flex items-center gap-2">
                        <Shuffle className="w-4 h-4" />
                        <span>New Reflection</span>
                      </div>
                    </button>
                  </div>
                </div>

                <div className="space-y-2 mb-6">
                  {copySuccess && (
                    <div className="flex items-center gap-2 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/60 dark:to-emerald-900/60 text-green-700 dark:text-green-300 border border-green-300 dark:border-green-700/50 shadow-lg rounded-lg px-4 py-2 animate-pulse">
                      <CheckCircle className="w-4 h-4" />
                      {copySuccess}
                    </div>
                  )}
                  {appendStatus && (
                    <div className="flex items-center gap-2 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/60 dark:to-emerald-900/60 text-green-700 dark:text-green-300 border border-green-300 dark:border-green-700/50 shadow-lg rounded-lg px-4 py-2 animate-pulse">
                      <CheckCircle className="w-4 h-4" />
                      {appendStatus}
                    </div>
                  )}
                  {dailyReflectionError && (
                    <div className="flex items-center gap-2 bg-gradient-to-r from-red-100 to-pink-100 dark:from-red-900/60 dark:to-pink-900/60 text-red-700 dark:text-red-300 border border-red-300 dark:border-red-700/50 shadow-lg rounded-lg px-4 py-2">
                      <XCircle className="w-4 h-4" />
                      {dailyReflectionError}
                    </div>
                  )}
                </div>

                {dailyReflection && (
                  <div className="relative bg-gradient-to-br from-gray-100 to-white dark:from-neutral-800/80 dark:to-neutral-700/80 rounded-2xl p-8 border border-gray-200 dark:border-neutral-600/50 shadow-2xl backdrop-blur-sm">
                    {/* Quote decoration */}
                    <div className="absolute top-4 left-4 text-6xl text-yellow-400/20 font-serif">
                      "
                    </div>
                    <div className="absolute bottom-4 right-4 text-6xl text-yellow-400/20 font-serif rotate-180">
                      "
                    </div>

                    <div className="relative z-10">
                      <blockquote className="text-2xl italic mb-6 text-gray-800 dark:text-neutral-100 leading-relaxed font-light">
                        "{dailyReflection.quote}"
                      </blockquote>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                        <div className="flex items-center gap-3 text-gray-700 dark:text-neutral-300">
                          <BookOpen className="w-5 h-5 text-blue-400" />
                          <div>
                            <span className="text-blue-600 dark:text-blue-300 font-medium">
                              From:
                            </span>{" "}
                            <span className="font-semibold">
                              {dailyReflection.bookTitle
                                ? dailyReflection.bookTitle
                                : dailyReflection.fileName}
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center gap-3 text-gray-700 dark:text-neutral-300">
                          <User className="w-5 h-5 text-purple-400" />
                          <div>
                            <span className="text-purple-600 dark:text-purple-300 font-medium">
                              By:
                            </span>{" "}
                            <span className="font-semibold">
                              {dailyReflection.author}
                            </span>
                          </div>
                        </div>

                        {dailyReflection.location && (
                          <div className="flex items-center gap-3 text-gray-700 dark:text-neutral-300">
                            <MapPin className="w-5 h-5 text-green-400" />
                            <div>
                              <span className="text-green-600 dark:text-green-300 font-medium">
                                Location:
                              </span>{" "}
                              <span className="font-semibold">
                                {dailyReflection.location}
                              </span>
                            </div>
                          </div>
                        )}

                        {dailyReflection.color && (
                          <div className="flex items-center gap-3 text-gray-700 dark:text-neutral-300">
                            <Palette className="w-5 h-5 text-neutral-400" />
                            <div className="flex items-center gap-2">
                              {settings?.customColors?.[
                                dailyReflection.color.toLowerCase()
                              ] && (
                                <span
                                  className="inline-block w-5 h-5 rounded-full border border-gray-300 dark:border-neutral-600"
                                  style={{
                                    background:
                                      settings.customColors[
                                        dailyReflection.color.toLowerCase()
                                      ],
                                  }}
                                  title={dailyReflection.color}
                                ></span>
                              )}
                              <span className="text-neutral-600 dark:text-neutral-300 font-medium">
                                {dailyReflection.color}
                              </span>
                            </div>
                          </div>
                        )}
                      </div>

                      {dailyReflection.note && (
                        <div className="mt-6 p-4 bg-gray-50 dark:bg-neutral-900/40 rounded-xl border border-gray-200 dark:border-neutral-600/30">
                          <div className="flex items-start gap-3">
                            <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                            <div>
                              <span className="text-yellow-600 dark:text-yellow-300 font-semibold text-sm">
                                Note:
                              </span>
                              <p className="text-gray-700 dark:text-neutral-300 text-sm mt-1 leading-relaxed">
                                {dailyReflection.note}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="relative bg-gradient-to-br from-white to-gray-50 dark:from-neutral-900 dark:via-neutral-800 dark:to-neutral-900 rounded-2xl p-8 shadow-2xl border border-gray-200 dark:border-neutral-700/50 backdrop-blur-sm overflow-hidden mb-8">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-green-500/5 to-purple-500/5 rounded-2xl pointer-events-none"></div>

              <div className="absolute top-4 right-4 w-16 h-16 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse"></div>
              <div className="absolute bottom-4 left-4 w-12 h-12 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-full blur-xl animate-pulse delay-1000"></div>

              <div className="relative z-10">
                <div className="flex items-center gap-4 mb-8">
                  <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                    <BookUserIcon className="w-7 h-7 text-white" />
                  </div>
                  <div>
                    <h3 className="text-3xl font-bold text-gray-800 dark:text-white bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                      Kindle Controls
                    </h3>
                    <p className="text-gray-600 dark:text-neutral-400 text-sm mt-1">
                      Manage your Kindle connection and sync your reading
                      highlights
                    </p>
                  </div>
                </div>

                <div className="mb-8 p-6 bg-gradient-to-br from-gray-100 to-white dark:from-neutral-800/80 dark:to-neutral-700/80 rounded-2xl border border-gray-200 dark:border-neutral-600/50 shadow-2xl backdrop-blur-sm">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-semibold text-gray-800 dark:text-white">
                      Connection Status
                    </h4>
                    <button
                      onClick={checkKindleConnection}
                      className="cursor-pointer group flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-sm font-medium rounded-lg shadow-lg hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-50 dark:focus:ring-offset-neutral-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
                      title="Check connection status"
                      disabled={connectionStatus.loading}
                    >
                      {connectionStatus.loading ? (
                        <RefreshCw className="w-4 h-4 animate-spin" />
                      ) : (
                        <RefreshCw className="w-4 h-4 group-hover:rotate-180 transition-transform duration-300" />
                      )}
                      <span>Check Status</span>
                    </button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-3 text-gray-700 dark:text-neutral-300">
                      <div
                        className={`relative w-6 h-6 rounded-full bg-gradient-to-r ${getStatusColor()} shadow-lg`}
                      >
                        <div className="absolute inset-0 flex items-center justify-center text-white text-xs">
                          {getStatusIcon()}
                        </div>
                        {connectionStatus.loading && (
                          <div className="absolute inset-0 rounded-full bg-gradient-to-r from-yellow-400 to-orange-400 animate-ping opacity-75"></div>
                        )}
                      </div>
                      <div>
                        <span className="text-blue-600 dark:text-blue-300 font-medium">
                          Status:
                        </span>{" "}
                        <span className="font-semibold">
                          {connectionStatus.loading
                            ? "Connecting..."
                            : connectionStatus.connected
                            ? "Connected to Kindle"
                            : "Not connected"}
                        </span>
                      </div>
                    </div>

                    {syncStatus.nextSyncTime && settings.autoSync && (
                      <div className="flex items-center gap-3 text-gray-700 dark:text-neutral-300">
                        <Clock className="w-5 h-5 text-blue-400" />
                        <div>
                          <span className="text-blue-600 dark:text-blue-300 font-medium">
                            Next Sync:
                          </span>{" "}
                          <span className="font-semibold">
                            {syncStatus.nextSyncTime}
                          </span>
                        </div>
                      </div>
                    )}

                    {connectionStatus.error && (
                      <div className="flex items-center gap-3 text-red-600 dark:text-red-300 md:col-span-2">
                        <AlertCircle className="w-5 h-5 text-red-400 animate-pulse" />
                        <div>
                          <span className="text-red-600 dark:text-red-300 font-medium">
                            Could not connect:
                          </span>{" "}
                          <span className="font-semibold">
                            Please establish a connection to Kindle below
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex flex-col gap-6">
                  <div className="flex flex-col gap-4">
                    <button
                      onClick={showBrowser}
                      className="group relative w-full overflow-hidden text-center cursor-pointer font-bold text-lg py-5 px-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl shadow-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 active:scale-[0.98] disabled:opacity-60 disabled:cursor-not-allowed transform hover:shadow-xl hover:shadow-blue-500/25 md:w-auto md:flex-grow"
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-white/0 to-white/10 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                      <div className="relative flex items-center justify-center gap-3">
                        <Link className="w-5 h-5" />
                        {connectionStatus.connected
                          ? "Adjust Kindle Connection"
                          : "Establish Kindle Connection"}
                      </div>
                    </button>

                    <button
                      onClick={handleFetchAll}
                      className="group relative w-full overflow-hidden text-center cursor-pointer font-bold text-lg py-5 px-6 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl shadow-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 active:scale-[0.98] disabled:opacity-60 disabled:cursor-not-allowed transform hover:shadow-xl hover:shadow-green-500/25 md:w-auto md:flex-grow"
                      disabled={
                        isLoading ||
                        !connectionStatus.connected ||
                        connectionStatus.loading
                      }
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-white/0 to-white/10 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                      <div className="relative flex items-center justify-center gap-3">
                        {isLoading ? (
                          <>
                            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            <span>Fetching...</span>
                          </>
                        ) : (
                          <>
                            <Download className="w-5 h-5" />
                            <span>Refresh Kindle Books</span>
                          </>
                        )}
                      </div>
                    </button>

                    {dataSource && !lastKindleSyncTime && (
                      <div className="flex items-center gap-2 px-6 py-4 rounded-xl text-sm font-medium bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/60 dark:to-emerald-900/60 text-green-700 dark:text-green-300 border border-green-300 dark:border-green-700/50 shadow-lg animate-pulse md:flex-shrink-0">
                        <CheckCircle className="w-4 h-4" />
                        <span>
                          Fetched new highlights from Kindle! Now applying them.
                        </span>
                      </div>
                    )}
                    {dataSource && lastKindleSyncTime && (
                      <div className="flex items-center gap-2 px-6 py-4 rounded-xl text-sm font-medium bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/60 dark:to-emerald-900/60 text-green-700 dark:text-green-300 border border-green-300 dark:border-green-700/50 shadow-lg md:flex-shrink-0">
                        <CheckCircle className="w-4 h-4" />
                        <span>
                          Last fetched and applied Kindle data at{" "}
                          {lastKindleSyncTime}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="mt-8 p-6 bg-gray-50 dark:bg-neutral-900/40 rounded-xl border border-gray-200 dark:border-neutral-600/30">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <span className="text-blue-600 dark:text-blue-300 font-semibold text-sm">
                        Don't Worry:
                      </span>
                      <p className="text-gray-700 dark:text-neutral-300 text-sm mt-1 leading-relaxed">
                        Your Amazon credentials are not visible to anyone but
                        you. They are not stored anywhere, as they are only used
                        to authenticate within the local browser in this app.
                        Once the in-app browser has the session, it will
                        continue to use that session until you log out.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {databaseStats && databaseStats.sourceCount === 0 ? (
              <div className="mb-4 flex items-center gap-2 px-6 py-4 rounded-xl text-sm font-medium bg-gradient-to-r from-yellow-100 to-orange-100 dark:from-yellow-900/60 dark:to-orange-900/60 text-yellow-700 dark:text-yellow-200 border border-yellow-300 dark:border-yellow-700/50 shadow-lg md:flex-shrink-0">
                <AlertCircle className="w-4 h-4" />
                <span>
                  No data found in the local database. Please connect your
                  Kindle and fetch data, or send data from KOReader.
                </span>
              </div>
            ) : null}

            <div className="relative bg-gradient-to-br from-white to-gray-50 dark:from-neutral-900 dark:via-neutral-800 dark:to-neutral-900 rounded-2xl p-8 shadow-2xl border border-gray-200 dark:border-neutral-700/50 backdrop-blur-sm overflow-hidden mb-8">
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-blue-500/5 to-purple-500/5 rounded-2xl pointer-events-none"></div>

              <div className="absolute top-4 right-4 w-16 h-16 bg-gradient-to-br from-emerald-400/20 to-blue-400/20 rounded-full blur-xl animate-pulse"></div>
              <div className="absolute bottom-4 left-4 w-12 h-12 bg-gradient-to-br from-purple-400/20 to-emerald-400/20 rounded-full blur-xl animate-pulse delay-1000"></div>

              <div className="relative z-10">
                <div className="flex items-center gap-4 mb-6">
                  <div className="p-3 bg-gradient-to-br from-emerald-500 to-blue-600 rounded-xl shadow-lg">
                    <svg
                      className="w-7 h-7 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"
                      />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-3xl font-bold text-gray-800 dark:text-white bg-gradient-to-r from-emerald-400 to-blue-400 bg-clip-text">
                      Database
                    </h3>
                    <p className="text-gray-600 dark:text-neutral-400 text-sm mt-1">
                      Overview of your stored reading data. Unearthed has its
                      own local database stored on your machine that is separate
                      to Obsidian.
                    </p>
                  </div>
                  {databaseStats &&
                    databaseStats.sourceCount &&
                    settings?.obsidianVaultPath && (
                      <button
                        onClick={async () => {
                          setResponseData(
                            "Exporting database data to Obsidian..."
                          );
                          try {
                            const result = await window.electronAPI.exportBooks(
                              settings,
                              allSources
                            );
                            if (result.success) {
                              setResponseData(
                                `Exported ${result.filesWritten} files from database successfully.`
                              );
                            } else {
                              setResponseData(result.error || "Export failed.");
                            }
                          } catch (e) {
                            setResponseData(
                              e instanceof Error ? e.message : String(e)
                            );
                          }
                        }}
                        className="cursor-pointer group relative flex items-center gap-2 px-5 py-2 bg-gradient-to-r from-emerald-600 to-blue-600 text-white font-semibold rounded-xl shadow-lg hover:from-emerald-700 hover:to-blue-700 transition-all duration-300 active:scale-95 overflow-hidden ml-4"
                        style={{ minHeight: "2.5rem" }}
                      >
                        <Download className="w-4 h-4" />
                        <span>Send to Obsidian</span>
                      </button>
                    )}
                </div>

                {statsLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="w-8 h-8 border-4 border-emerald-500 border-t-transparent rounded-full animate-spin"></div>
                    <span className="ml-3 text-gray-600 dark:text-gray-400">
                      Loading database statistics...
                    </span>
                  </div>
                ) : databaseStats ? (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="bg-gradient-to-br from-emerald-50 to-blue-50 dark:from-emerald-900/20 dark:to-blue-900/20 rounded-xl p-6 border border-emerald-200 dark:border-emerald-700/30">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="p-2 bg-emerald-500 rounded-lg">
                            <BookOpen className="w-5 h-5 text-white" />
                          </div>
                          <span className="text-emerald-600 dark:text-emerald-300 font-semibold">
                            Sources
                          </span>
                        </div>
                        <div className="text-3xl font-bold text-gray-800 dark:text-white">
                          {databaseStats.sourceCount}
                        </div>
                        <p className="text-gray-600 dark:text-gray-400 text-sm">
                          Including ignored
                        </p>
                      </div>

                      <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-6 border border-purple-200 dark:border-purple-700/30">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="p-2 bg-purple-500 rounded-lg">
                            <svg
                              className="w-5 h-5 text-white"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"
                              />
                            </svg>
                          </div>
                          <span className="text-purple-600 dark:text-purple-300 font-semibold">
                            Quotes
                          </span>
                        </div>
                        <div className="text-3xl font-bold text-gray-800 dark:text-white">
                          {databaseStats.quoteCount}
                        </div>
                        <p className="text-gray-600 dark:text-gray-400 text-sm">
                          Including ignored
                        </p>
                      </div>
                    </div>

                    {allSources.length > 0 && (
                      <div className="bg-gray-50 dark:bg-neutral-900/40 rounded-xl border border-gray-200 dark:border-neutral-600/30">
                        {/* Explanation about ignored sources */}
                        <div className="px-4 pt-4 pb-2">
                          <div className="flex items-center gap-2 text-xs text-yellow-700 dark:text-yellow-200 bg-yellow-50 dark:bg-yellow-900/30 rounded-lg p-2 mb-2">
                            <EyeOff className="w-4 h-4" />
                            <span>
                              Sources marked as <b>ignored</b> will not be
                              exported to Obsidian. Toggle the{" "}
                              <Eye className="inline w-4 h-4" />/
                              <EyeOff className="inline w-4 h-4" /> icon to
                              include or exclude a source from export.
                            </span>
                          </div>
                        </div>
                        <button
                          onClick={() => setShowSources(!showSources)}
                          className="cursor-pointer w-full flex items-center justify-between p-4 text-left hover:bg-gray-100 dark:hover:bg-neutral-800/60 transition-colors duration-200 rounded-xl"
                        >
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-blue-500 rounded-lg">
                              <svg
                                className="w-4 h-4 text-white"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 9l-7 7-7-7"
                                />
                              </svg>
                            </div>
                            <span className="font-semibold text-gray-800 dark:text-white">
                              View All Sources ({activeCount} active,{" "}
                              {ignoredCount} ignored)
                            </span>
                          </div>
                          <div
                            className={`transform transition-transform duration-200 ${
                              showSources ? "rotate-180" : ""
                            }`}
                          >
                            <svg
                              className="w-5 h-5 text-gray-500"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 9l-7 7-7-7"
                              />
                            </svg>
                          </div>
                        </button>

                        {showSources && (
                          <div className="px-4 pb-4">
                            <div className="space-y-2 max-h-[700px] overflow-y-auto">
                              {[
                                ...allSources.filter((s) => !s.ignored),
                                ...allSources.filter((s) => s.ignored),
                              ].map((source, index) => (
                                <div
                                  key={source.id || index}
                                  className={`flex items-center justify-between p-3 rounded-lg border transition-opacity duration-200 ${
                                    source.ignored
                                      ? "bg-gray-200 dark:bg-neutral-900/60 border-gray-300 dark:border-neutral-700/50 opacity-60 italic"
                                      : "bg-white dark:bg-neutral-800/60 border-gray-200 dark:border-neutral-600/30"
                                  }`}
                                >
                                  <div className="flex-1 min-w-0">
                                    <div className="font-medium text-gray-800 dark:text-white truncate flex items-center gap-2">
                                      {source.title}
                                      {source.ignored && (
                                        <span className="ml-2 px-2 py-0.5 text-xs font-semibold rounded-full bg-yellow-200 text-yellow-800 dark:bg-yellow-700 dark:text-yellow-100">
                                          Ignored
                                        </span>
                                      )}
                                    </div>
                                    {source.author && (
                                      <div className="text-sm text-gray-600 dark:text-gray-400 truncate">
                                        by {source.author}
                                      </div>
                                    )}
                                    <div className="flex items-center gap-4 mt-1">
                                      <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                                        <svg
                                          className="w-3 h-3"
                                          fill="none"
                                          stroke="currentColor"
                                          viewBox="0 0 24 24"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"
                                          />
                                        </svg>
                                        <span>
                                          {source.quoteCount || 0} quotes
                                        </span>
                                      </div>
                                      <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                                        <svg
                                          className="w-3 h-3"
                                          fill="none"
                                          stroke="currentColor"
                                          viewBox="0 0 24 24"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                          />
                                        </svg>
                                        <span>
                                          {source.noteCount || 0} notes
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-2 ml-4">
                                    {source.type && (
                                      <span className="px-2 py-1 text-xs font-medium bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300 rounded-full">
                                        {source.type}
                                      </span>
                                    )}
                                    {source.origin && (
                                      <span className="px-2 py-1 text-xs font-medium bg-green-100 dark:bg-green-900/40 text-green-700 dark:text-green-300 rounded-full">
                                        {source.origin}
                                      </span>
                                    )}
                                    <button
                                      onClick={() =>
                                        handleToggleIgnored(
                                          source.id,
                                          source.ignored
                                        )
                                      }
                                      className={`cursor-pointer p-1.5 rounded transition-colors duration-200 text-yellow-600 hover:text-yellow-800 hover:bg-yellow-100 dark:hover:bg-yellow-900/20 ${
                                        source.ignored ? "font-bold" : ""
                                      }`}
                                      title={
                                        source.ignored
                                          ? "Unignore this source"
                                          : "Ignore this source"
                                      }
                                    >
                                      {source.ignored ? (
                                        <EyeOff className="w-4 h-4" />
                                      ) : (
                                        <Eye className="w-4 h-4" />
                                      )}
                                    </button>
                                    <div className="flex items-center gap-1">
                                      <button
                                        onClick={() =>
                                          handleDeleteQuotesBySource(
                                            source.id,
                                            source.title
                                          )
                                        }
                                        className="cursor-pointer p-1.5 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                        disabled={
                                          deleteLoading.sources[source.id]
                                        }
                                        title="Delete all quotes from this source"
                                      >
                                        {deleteLoading.sources[source.id] ? (
                                          <div className="w-3 h-3 border border-red-500 border-t-transparent rounded-full animate-spin"></div>
                                        ) : (
                                          <svg
                                            className="w-3 h-3"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                          >
                                            <path
                                              strokeLinecap="round"
                                              strokeLinejoin="round"
                                              strokeWidth={2}
                                              d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"
                                            />
                                          </svg>
                                        )}
                                      </button>
                                      <button
                                        onClick={() =>
                                          handleDeleteSource(
                                            source.id,
                                            source.title
                                          )
                                        }
                                        className="cursor-pointer p-1.5 text-red-600 hover:text-red-800 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                        disabled={
                                          deleteLoading.sources[source.id]
                                        }
                                        title="Delete this source and all its quotes"
                                      >
                                        {deleteLoading.sources[source.id] ? (
                                          <div className="w-3 h-3 border border-red-600 border-t-transparent rounded-full animate-spin"></div>
                                        ) : (
                                          <XCircle className="w-3 h-3" />
                                        )}
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    <div className="flex justify-between items-center">
                      <button
                        onClick={handleClearAllData}
                        className="cursor-pointer flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-red-700 text-white text-sm font-medium rounded-lg shadow-lg hover:from-red-700 hover:to-red-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={deleteLoading.clearAll || statsLoading}
                        title="Delete all sources and quotes"
                      >
                        {deleteLoading.clearAll ? (
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        ) : (
                          <XCircle className="w-4 h-4" />
                        )}
                        Clear All Data
                      </button>

                      <button
                        onClick={loadDatabaseStats}
                        className="cursor-pointer flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-emerald-600 to-blue-600 text-white text-sm font-medium rounded-lg shadow-lg hover:from-emerald-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={statsLoading}
                      >
                        {statsLoading ? (
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        ) : (
                          <RefreshCw className="w-4 h-4" />
                        )}
                        Refresh Stats
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-600 dark:text-gray-400">
                    <svg
                      className="w-12 h-12 mx-auto mb-4 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    <p>No database statistics available</p>
                    <p className="text-sm">
                      Try refreshing or importing some data
                    </p>
                  </div>
                )}
              </div>
            </div>

            <div className="fixed bottom-0 left-0 right-0 bg-neutral-800 shadow-lg border-t border-neutral-700">
              <div className="container mx-auto px-4 py-3">
                <div className="flex justify-between items-center">
                  <h3 className="text-sm font-medium text-neutral-300">
                    Application Status
                  </h3>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <div
                        className={`w-2 h-2 rounded-full ${
                          syncStatus.isRunning
                            ? "bg-yellow-400 animate-pulse"
                            : syncStatus.lastError
                            ? "bg-red-400"
                            : syncStatus.hasRunInitial
                            ? "bg-green-400"
                            : "bg-gray-400"
                        }`}
                      ></div>
                      <span className="text-xs text-neutral-400">
                        {syncStatus.isRunning
                          ? "Syncing..."
                          : syncStatus.lastError
                          ? "Sync Failed"
                          : syncStatus.hasRunInitial
                          ? "Synced"
                          : "Initializing..."}
                      </span>
                    </div>
                  </div>
                </div>
                <div
                  className="mt-2 bg-neutral-900 rounded-lg p-3 overflow-auto font-mono text-sm text-neutral-300 border border-neutral-700 custom-scrollbar"
                  style={{ maxHeight: "150px" }}
                >
                  <pre className="whitespace-pre-wrap break-words">
                    {responseData ||
                      "No log data yet. Actions and events will be displayed here."}
                  </pre>
                </div>
              </div>
            </div>
            <div className="pb-40"></div>
          </div>
        </div>

        <SettingsModal
          isOpen={settingsOpen}
          onClose={() => setSettingsOpen(false)}
          settings={settings}
          handleSettingsChange={handleSettingsChange}
          handleColorChange={handleColorChange}
          pickVaultFolder={pickVaultFolder}
          saveSettings={saveSettings}
          apiUrls={apiUrls}
        />
      </div>
    </>
  );
};

const root = createRoot(document.body);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
