/**
 * Data validation utilities for database operations
 */

import { NewSource, NewQuote, Source, Quote } from "../db/schema";

export class ValidationError extends Error {
  constructor(message: string, public field?: string, public value?: any) {
    super(message);
    this.name = "ValidationError";
  }
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

/**
 * Create a validation result
 */
function createValidationResult(
  errors: ValidationError[] = []
): ValidationResult {
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Validate string field
 */
function validateString(
  value: any,
  fieldName: string,
  options: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    allowEmpty?: boolean;
  } = {}
): ValidationError[] {
  const errors: ValidationError[] = [];
  const { required = false, minLength, maxLength, allowEmpty = true } = options;

  // Check if required
  if (required && (value === undefined || value === null)) {
    errors.push(
      new ValidationError(`${fieldName} is required`, fieldName, value)
    );
    return errors;
  }

  // Skip further validation if not required and empty
  if (!required && (value === undefined || value === null)) {
    return errors;
  }

  // Check if string
  if (typeof value !== "string") {
    errors.push(
      new ValidationError(`${fieldName} must be a string`, fieldName, value)
    );
    return errors;
  }

  // Check empty string
  if (!allowEmpty && value.trim() === "") {
    errors.push(
      new ValidationError(`${fieldName} cannot be empty`, fieldName, value)
    );
  }

  // Check minimum length
  if (minLength !== undefined && value.length < minLength) {
    errors.push(
      new ValidationError(
        `${fieldName} must be at least ${minLength} characters long`,
        fieldName,
        value
      )
    );
  }

  // Check maximum length
  if (maxLength !== undefined && value.length > maxLength) {
    errors.push(
      new ValidationError(
        `${fieldName} must be no more than ${maxLength} characters long`,
        fieldName,
        value
      )
    );
  }

  return errors;
}

/**
 * Validate number field
 */
function validateNumber(
  value: any,
  fieldName: string,
  options: {
    required?: boolean;
    min?: number;
    max?: number;
    integer?: boolean;
  } = {}
): ValidationError[] {
  const errors: ValidationError[] = [];
  const { required = false, min, max, integer = false } = options;

  // Check if required
  if (required && (value === undefined || value === null)) {
    errors.push(
      new ValidationError(`${fieldName} is required`, fieldName, value)
    );
    return errors;
  }

  // Skip further validation if not required and empty
  if (!required && (value === undefined || value === null)) {
    return errors;
  }

  // Check if number
  if (typeof value !== "number" || isNaN(value)) {
    errors.push(
      new ValidationError(
        `${fieldName} must be a valid number`,
        fieldName,
        value
      )
    );
    return errors;
  }

  // Check if integer
  if (integer && !Number.isInteger(value)) {
    errors.push(
      new ValidationError(`${fieldName} must be an integer`, fieldName, value)
    );
  }

  // Check minimum value
  if (min !== undefined && value < min) {
    errors.push(
      new ValidationError(
        `${fieldName} must be at least ${min}`,
        fieldName,
        value
      )
    );
  }

  // Check maximum value
  if (max !== undefined && value > max) {
    errors.push(
      new ValidationError(
        `${fieldName} must be no more than ${max}`,
        fieldName,
        value
      )
    );
  }

  return errors;
}

/**
 * Validate boolean field
 */
function validateBoolean(
  value: any,
  fieldName: string,
  options: { required?: boolean } = {}
): ValidationError[] {
  const errors: ValidationError[] = [];
  const { required = false } = options;

  // Check if required
  if (required && (value === undefined || value === null)) {
    errors.push(
      new ValidationError(`${fieldName} is required`, fieldName, value)
    );
    return errors;
  }

  // Skip further validation if not required and empty
  if (!required && (value === undefined || value === null)) {
    return errors;
  }

  // Check if boolean
  if (typeof value !== "boolean") {
    errors.push(
      new ValidationError(`${fieldName} must be a boolean`, fieldName, value)
    );
  }

  return errors;
}

/**
 * Validate Date field
 */
function validateDate(
  value: any,
  fieldName: string,
  options: { required?: boolean } = {}
): ValidationError[] {
  const errors: ValidationError[] = [];
  const { required = false } = options;

  // Check if required
  if (required && (value === undefined || value === null)) {
    errors.push(
      new ValidationError(`${fieldName} is required`, fieldName, value)
    );
    return errors;
  }

  // Skip further validation if not required and empty
  if (!required && (value === undefined || value === null)) {
    return errors;
  }

  // Check if valid date
  if (!(value instanceof Date) || isNaN(value.getTime())) {
    errors.push(
      new ValidationError(`${fieldName} must be a valid date`, fieldName, value)
    );
  }

  return errors;
}

/**
 * Validate NewSource data
 */
export function validateNewSource(data: any): ValidationResult {
  const errors: ValidationError[] = [];

  // Validate title (required)
  errors.push(
    ...validateString(data.title, "title", {
      required: true,
      minLength: 1,
      maxLength: 500,
      allowEmpty: false,
    })
  );

  // Validate subtitle (optional)
  errors.push(
    ...validateString(data.subtitle, "subtitle", {
      maxLength: 500,
    })
  );

  // Validate author (optional, but defaults to empty string)
  errors.push(
    ...validateString(data.author, "author", {
      maxLength: 200,
    })
  );

  // Validate type (optional)
  errors.push(
    ...validateString(data.type, "type", {
      maxLength: 50,
    })
  );

  // Validate origin (optional)
  errors.push(
    ...validateString(data.origin, "origin", {
      maxLength: 100,
    })
  );

  // Validate asin (optional)
  errors.push(
    ...validateString(data.asin, "asin", {
      maxLength: 20,
    })
  );

  // Validate mediaId (optional)
  errors.push(
    ...validateString(data.mediaId, "mediaId", {
      maxLength: 100,
    })
  );

  // Validate ignored (optional boolean)
  errors.push(...validateBoolean(data.ignored, "ignored"));

  // Validate createdAt (optional date)
  if (data.createdAt !== undefined) {
    errors.push(...validateDate(data.createdAt, "createdAt"));
  }

  return createValidationResult(errors);
}

/**
 * Validate incoming quote data (before sourceId is assigned)
 */
export function validateIncomingQuote(data: any): ValidationResult {
  const errors: ValidationError[] = [];

  // Validate content (required)
  errors.push(
    ...validateString(data.content, "content", {
      required: true,
      minLength: 1,
      maxLength: 10000,
      allowEmpty: false,
    })
  );

  // Validate note (optional)
  errors.push(
    ...validateString(data.note, "note", {
      maxLength: 1000000, // Increased to 1MB to handle very long Kindle notes
    })
  );

  // Validate color (optional)
  errors.push(
    ...validateString(data.color, "color", {
      maxLength: 20,
    })
  );

  // Validate location (optional)
  errors.push(
    ...validateString(data.location, "location", {
      maxLength: 100,
    })
  );

  // Note: sourceId is not validated here as it will be set by the API

  // Validate createdAt (optional date)
  if (data.createdAt !== undefined) {
    errors.push(...validateDate(data.createdAt, "createdAt"));
  }

  return createValidationResult(errors);
}

/**
 * Validate NewQuote data
 */
export function validateNewQuote(data: any): ValidationResult {
  const errors: ValidationError[] = [];

  // Validate content (required)
  errors.push(
    ...validateString(data.content, "content", {
      required: true,
      minLength: 1,
      maxLength: 10000,
      allowEmpty: false,
    })
  );

  // Validate note (optional)
  errors.push(
    ...validateString(data.note, "note", {
      maxLength: 1000000, // Increased to 1MB to handle very long Kindle notes
    })
  );

  // Validate color (optional)
  errors.push(
    ...validateString(data.color, "color", {
      maxLength: 20,
    })
  );

  // Validate location (optional)
  errors.push(
    ...validateString(data.location, "location", {
      maxLength: 100,
    })
  );

  // Validate sourceId (required)
  errors.push(
    ...validateNumber(data.sourceId, "sourceId", {
      required: true,
      integer: true,
      min: 1,
    })
  );

  // Validate createdAt (optional date)
  if (data.createdAt !== undefined) {
    errors.push(...validateDate(data.createdAt, "createdAt"));
  }

  return createValidationResult(errors);
}

/**
 * Validate Source update data
 */
export function validateSourceUpdate(data: any): ValidationResult {
  const errors: ValidationError[] = [];

  // All fields are optional for updates, but if provided, must be valid
  if (data.title !== undefined) {
    errors.push(
      ...validateString(data.title, "title", {
        minLength: 1,
        maxLength: 500,
        allowEmpty: false,
      })
    );
  }

  if (data.subtitle !== undefined) {
    errors.push(
      ...validateString(data.subtitle, "subtitle", {
        maxLength: 500,
      })
    );
  }

  if (data.author !== undefined) {
    errors.push(
      ...validateString(data.author, "author", {
        maxLength: 200,
      })
    );
  }

  if (data.type !== undefined) {
    errors.push(
      ...validateString(data.type, "type", {
        maxLength: 50,
      })
    );
  }

  if (data.origin !== undefined) {
    errors.push(
      ...validateString(data.origin, "origin", {
        maxLength: 100,
      })
    );
  }

  if (data.asin !== undefined) {
    errors.push(
      ...validateString(data.asin, "asin", {
        maxLength: 20,
      })
    );
  }

  if (data.mediaId !== undefined) {
    errors.push(
      ...validateString(data.mediaId, "mediaId", {
        maxLength: 100,
      })
    );
  }

  if (data.ignored !== undefined) {
    errors.push(...validateBoolean(data.ignored, "ignored"));
  }

  if (data.createdAt !== undefined) {
    errors.push(...validateDate(data.createdAt, "createdAt"));
  }

  return createValidationResult(errors);
}

/**
 * Validate Quote update data
 */
export function validateQuoteUpdate(data: any): ValidationResult {
  const errors: ValidationError[] = [];

  // All fields are optional for updates, but if provided, must be valid
  if (data.content !== undefined) {
    errors.push(
      ...validateString(data.content, "content", {
        minLength: 1,
        maxLength: 10000,
        allowEmpty: false,
      })
    );
  }

  if (data.note !== undefined) {
    errors.push(
      ...validateString(data.note, "note", {
        maxLength: 1000000, // Increased to 1MB to handle very long Kindle notes
      })
    );
  }

  if (data.color !== undefined) {
    errors.push(
      ...validateString(data.color, "color", {
        maxLength: 20,
      })
    );
  }

  if (data.location !== undefined) {
    errors.push(
      ...validateString(data.location, "location", {
        maxLength: 100,
      })
    );
  }

  if (data.sourceId !== undefined) {
    errors.push(
      ...validateNumber(data.sourceId, "sourceId", {
        integer: true,
        min: 1,
      })
    );
  }

  if (data.createdAt !== undefined) {
    errors.push(...validateDate(data.createdAt, "createdAt"));
  }

  return createValidationResult(errors);
}

/**
 * Validate ID parameter
 */
export function validateId(id: any): ValidationResult {
  const errors: ValidationError[] = [];

  errors.push(
    ...validateNumber(id, "id", {
      required: true,
      integer: true,
      min: 1,
    })
  );

  return createValidationResult(errors);
}

/**
 * Sanitize string input by trimming whitespace and handling null/undefined
 */
export function sanitizeString(value: any): string | undefined {
  if (value === null || value === undefined) {
    return undefined;
  }

  if (typeof value !== "string") {
    return String(value).trim();
  }

  return value.trim();
}

/**
 * Sanitize NewSource data
 */
export function sanitizeNewSource(data: any): NewSource {
  return {
    title: sanitizeString(data.title) || "",
    subtitle: sanitizeString(data.subtitle),
    author: sanitizeString(data.author) || "",
    type: sanitizeString(data.type),
    origin: sanitizeString(data.origin),
    asin: sanitizeString(data.asin),
    mediaId: sanitizeString(data.mediaId),
    ignored: Boolean(data.ignored),
    createdAt: data.createdAt instanceof Date ? data.createdAt : new Date(),
  };
}

/**
 * Sanitize NewQuote data
 */
export function sanitizeNewQuote(data: any): NewQuote {
  return {
    content: sanitizeString(data.content) || "",
    note: sanitizeString(data.note) || "",
    color: sanitizeString(data.color),
    location: sanitizeString(data.location),
    sourceId: Number(data.sourceId),
    createdAt: data.createdAt instanceof Date ? data.createdAt : new Date(),
  };
}
