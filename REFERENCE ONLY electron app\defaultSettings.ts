import type { AppSettings } from "../components/SettingsModal";

export const DEFAULT_SETTINGS: AppSettings = {
  obsidianVaultPath: "",
  autoSync: true,
  kindleUrl: "https://read.amazon.com",
  dailyReflectionDateFormat: "YYYY-MM-DD",
  dailyReflectionLocation: "Daily Notes",
  addDailyReflection: true,
  quoteTemplate: "",
  sourceTemplate: "",
  sourceFilenameTemplate: "{{title}}",
  sourceFilenameLowercase: false,
  sourceFilenameReplaceSpaces: " ",
  dailyReflectionTemplate: "",
  rootFolder: "Unearthed",
  quoteColorMode: "background",
  customColors: {
    yellow: "#ffd700",
    blue: "#4682b4",
    pink: "#ff69b4",
    orange: "#ffa500",
    red: "#ff4d4f",
    green: "#52c41a",
    olive: "#b5b35c",
    cyan: "#13c2c2",
    purple: "#a259d9",
    gray: "#888888",
  },
  createDailyNoteIfNotExist: false,
  keepAppRunning: true,
  tokenSecret: "",
  runOnStartup: true,
};
